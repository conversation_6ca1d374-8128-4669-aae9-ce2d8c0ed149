
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, FileText, Download, Eye } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import BottomNavigation from "@/components/BottomNavigation";

const BOEGeneratorScreen = () => {
  const navigate = useNavigate();
  const [openSections, setOpenSections] = useState<string[]>(['importer']);

  const toggleSection = (section: string) => {
    setOpenSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const sections = [
    {
      id: 'importer',
      title: 'Importer Details',
      status: 'complete',
      fields: ['Company Name', 'VAT Number', 'Import Permit']
    },
    {
      id: 'customs',
      title: 'Customs Procedure Codes',
      status: 'complete',
      fields: ['Procedure Code', 'Additional Codes', 'Preference Code']
    },
    {
      id: 'duty',
      title: 'Duty Calculation Breakdown',
      status: 'complete',
      fields: ['Customs Value', 'Duty Rate', 'VAT Calculation']
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-sars-blue text-white p-4">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/verification')}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-lg font-semibold">BOE Generator</h1>
            <p className="text-blue-200 text-sm">Draft BOE-2024-004</p>
          </div>
        </div>
      </div>

      {/* Split View Container */}
      <div className="flex flex-col lg:flex-row h-full">
        {/* SARS Form Section */}
        <div className="flex-1 p-4">
          <Card className="mb-4">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                SARS Bill of Entry Form
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Review and complete the BOE form sections below
              </p>

              <div className="space-y-3">
                {sections.map((section) => (
                  <Collapsible
                    key={section.id}
                    open={openSections.includes(section.id)}
                    onOpenChange={() => toggleSection(section.id)}
                  >
                    <CollapsibleTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-between p-4 h-auto"
                      >
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${
                            section.status === 'complete' ? 'bg-sars-green' : 'bg-yellow-500'
                          }`} />
                          <span className="font-medium">{section.title}</span>
                        </div>
                        <span className="text-xs text-gray-500">
                          {openSections.includes(section.id) ? 'Collapse' : 'Expand'}
                        </span>
                      </Button>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="mt-2">
                      <Card className="border-l-4 border-l-sars-green">
                        <CardContent className="p-4">
                          <div className="grid grid-cols-1 gap-3">
                            {section.fields.map((field, index) => (
                              <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                                <span className="text-sm">{field}</span>
                                <span className="text-xs text-sars-green">✓ Complete</span>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </CollapsibleContent>
                  </Collapsible>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Invoice Preview Section */}
        <div className="flex-1 p-4 lg:border-l">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="w-5 h-5" />
                Original Invoice
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-100 h-64 rounded-lg flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <FileText className="w-12 h-12 mx-auto mb-2" />
                  <p className="text-sm">Invoice Preview</p>
                  <p className="text-xs">Tech Suppliers Ltd</p>
                </div>
              </div>
              
              {/* Mapping Indicators */}
              <div className="mt-4 space-y-2">
                <div className="flex items-center gap-2 p-2 bg-sars-green/10 rounded">
                  <div className="w-2 h-2 bg-sars-green rounded-full"></div>
                  <span className="text-xs">Supplier VAT → BOE Section 8</span>
                </div>
                <div className="flex items-center gap-2 p-2 bg-sars-green/10 rounded">
                  <div className="w-2 h-2 bg-sars-green rounded-full"></div>
                  <span className="text-xs">Line Items → BOE Section 31</span>
                </div>
                <div className="flex items-center gap-2 p-2 bg-sars-green/10 rounded">
                  <div className="w-2 h-2 bg-sars-green rounded-full"></div>
                  <span className="text-xs">Total Value → BOE Section 22</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="p-4 space-y-3">
        <Button
          onClick={() => navigate('/review')}
          className="w-full bg-sars-green hover:bg-green-600"
        >
          <Download className="w-4 h-4 mr-2" />
          Generate PDF
        </Button>
        <div className="grid grid-cols-2 gap-3">
          <Button variant="outline">
            Save Draft
          </Button>
          <Button variant="outline">
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
        </div>
      </div>

      <BottomNavigation currentPage="capture" />
    </div>
  );
};

export default BOEGeneratorScreen;
