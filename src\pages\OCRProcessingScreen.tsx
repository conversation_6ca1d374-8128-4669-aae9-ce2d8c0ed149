
import { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useNavigate } from "react-router-dom";

const OCRProcessingScreen = () => {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState("Analyzing image...");
  const navigate = useNavigate();

  const steps = [
    "Analyzing image...",
    "Extracting supplier details...",
    "Reading line items...",
    "Validating data...",
    "Processing complete!"
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev) => {
        const newProgress = prev + 5;
        const stepIndex = Math.floor(newProgress / 20);
        
        if (stepIndex < steps.length) {
          setCurrentStep(steps[stepIndex]);
        }

        if (newProgress >= 100) {
          clearInterval(interval);
          setTimeout(() => navigate('/verification'), 1000);
          return 100;
        }
        
        return newProgress;
      });
    }, 200);

    return () => clearInterval(interval);
  }, [navigate]);

  return (
    <div className="min-h-screen bg-sars-blue flex flex-col items-center justify-center p-6">
      {/* SARS Logo Animation */}
      <div className="mb-12">
        <div className="w-24 h-24 bg-white rounded-full flex items-center justify-center mb-6 animate-pulse">
          <div className="text-sars-blue font-bold text-3xl">SARS</div>
        </div>
        <h1 className="text-2xl font-bold text-white text-center">Processing Invoice</h1>
      </div>

      {/* Progress Card */}
      <Card className="w-full max-w-md bg-white/10 backdrop-blur-md border-white/20 p-8">
        <div className="text-center text-white mb-6">
          <h2 className="text-lg font-medium mb-2">{currentStep}</h2>
          <p className="text-blue-200 text-sm">Extracting data using advanced OCR technology</p>
        </div>

        {/* Progress Bar */}
        <div className="space-y-4">
          <Progress value={progress} className="h-3" />
          <div className="flex justify-between text-white text-sm">
            <span>{progress}%</span>
            <span>Complete</span>
          </div>
        </div>

        {/* Processing Animation */}
        <div className="mt-8 flex justify-center">
          <div className="flex space-x-2">
            <div className="w-3 h-3 bg-sars-green rounded-full animate-pulse"></div>
            <div className="w-3 h-3 bg-sars-green rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-3 h-3 bg-sars-green rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
          </div>
        </div>
      </Card>

      {/* Preview Hint */}
      <div className="mt-8 text-center text-blue-200">
        <p className="text-sm">Processing may take 30-60 seconds</p>
        <p className="text-xs mt-1">Please keep the app open</p>
      </div>
    </div>
  );
};

export default OCRProcessingScreen;
