
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useNavigate } from "react-router-dom";
import { CheckCircle, AlertTriangle, ArrowLeft, Search } from "lucide-react";
import BottomNavigation from "@/components/BottomNavigation";

const DataVerificationScreen = () => {
  const navigate = useNavigate();
  const [supplierData, setSupplierData] = useState({
    name: "Tech Suppliers Ltd",
    vatNumber: "4123456789",
    address: "123 Business Street, Cape Town, 8001"
  });

  const [lineItems] = useState([
    {
      description: "Data processing machines",
      hsCode: "8471.30",
      quantity: 5,
      unitPrice: 3000,
      totalValue: 15000,
      validated: true
    },
    {
      description: "Computer monitors",
      hsCode: "",
      quantity: 10,
      unitPrice: 800,
      totalValue: 8000,
      validated: false
    }
  ]);

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-sars-blue text-white p-4">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/processing')}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-lg font-semibold">Verify Data</h1>
            <p className="text-blue-200 text-sm">Review extracted information</p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <Tabs defaultValue="supplier" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="supplier">Supplier</TabsTrigger>
            <TabsTrigger value="items">Items</TabsTrigger>
            <TabsTrigger value="calculations">Calculations</TabsTrigger>
          </TabsList>

          {/* Supplier Tab */}
          <TabsContent value="supplier" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-sars-green" />
                  Supplier Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="supplier-name">Company Name</Label>
                  <Input
                    id="supplier-name"
                    value={supplierData.name}
                    onChange={(e) => setSupplierData({...supplierData, name: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="vat-number">VAT Number</Label>
                  <div className="flex gap-2">
                    <Input
                      id="vat-number"
                      value={supplierData.vatNumber}
                      onChange={(e) => setSupplierData({...supplierData, vatNumber: e.target.value})}
                    />
                    <div className="flex items-center text-sars-green">
                      <CheckCircle className="w-5 h-5" />
                    </div>
                  </div>
                  <p className="text-xs text-sars-green mt-1">✅ Valid VAT Number</p>
                </div>
                <div>
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    value={supplierData.address}
                    onChange={(e) => setSupplierData({...supplierData, address: e.target.value})}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Items Tab */}
          <TabsContent value="items" className="mt-4">
            <div className="space-y-4">
              {lineItems.map((item, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="flex-1">
                        <h3 className="font-medium">{item.description}</h3>
                        <div className="grid grid-cols-2 gap-2 mt-2 text-sm">
                          <div>
                            <span className="text-gray-500">Qty:</span> {item.quantity}
                          </div>
                          <div>
                            <span className="text-gray-500">Unit Price:</span> R{item.unitPrice.toLocaleString()}
                          </div>
                        </div>
                        <div className="flex items-center gap-2 mt-2">
                          <Label className="text-xs">HS Code:</Label>
                          <Input
                            value={item.hsCode}
                            placeholder="Enter HS Code"
                            className="h-8 text-sm"
                          />
                          <Button size="sm" variant="outline">
                            <Search className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="text-right">
                        {item.validated ? (
                          <CheckCircle className="w-5 h-5 text-sars-green" />
                        ) : (
                          <AlertTriangle className="w-5 h-5 text-yellow-500" />
                        )}
                        <div className="text-sm font-medium mt-1">
                          R{item.totalValue.toLocaleString()}
                        </div>
                      </div>
                    </div>
                    {!item.validated && (
                      <p className="text-xs text-yellow-600 mt-2">❗ Missing HS Code</p>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Calculations Tab */}
          <TabsContent value="calculations" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Duty Calculation</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Customs Value:</span>
                    <span className="font-medium">R 23,000</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>+ Duty (10%):</span>
                    <span>R 2,300</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>+ VAT (15%):</span>
                    <span>R 3,795</span>
                  </div>
                  <hr />
                  <div className="flex justify-between font-semibold text-sars-blue">
                    <span>Total Amount:</span>
                    <span>R 29,095</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="mt-6 space-y-3">
          <Button
            onClick={() => navigate('/generator')}
            className="w-full bg-sars-green hover:bg-green-600"
          >
            Generate BOE
          </Button>
          <Button
            variant="outline"
            className="w-full border-sars-blue text-sars-blue hover:bg-sars-blue hover:text-white"
          >
            Auto-fill from previous
          </Button>
        </div>
      </div>

      <BottomNavigation currentPage="capture" />
    </div>
  );
};

export default DataVerificationScreen;
