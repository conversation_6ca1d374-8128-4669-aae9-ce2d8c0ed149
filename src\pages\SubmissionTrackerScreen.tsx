
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { CheckCircle, Clock, AlertCircle, Download, Eye } from "lucide-react";
import BottomNavigation from "@/components/BottomNavigation";

const SubmissionTrackerScreen = () => {
  const navigate = useNavigate();

  const submissions = [
    {
      id: "BOE-2024-004",
      supplier: "Tech Suppliers Ltd",
      submittedDate: "2024-01-15 14:30",
      status: "processing",
      timeline: [
        { step: "BOE Submitted", completed: true, time: "14:30" },
        { step: "SARS Processing", completed: true, time: "14:45" },
        { step: "Case Number Assigned", completed: false, time: "Est. 15:30" },
        { step: "Approval", completed: false, time: "Est. Tomorrow" }
      ],
      caseNumber: "IT150-2024-0001",
      estimatedCompletion: "24 hours"
    },
    {
      id: "BOE-2024-003",
      supplier: "Global Trade Co",
      submittedDate: "2024-01-14 10:15",
      status: "approved",
      timeline: [
        { step: "BOE Submitted", completed: true, time: "10:15" },
        { step: "SARS Processing", completed: true, time: "10:30" },
        { step: "Case Number Assigned", completed: true, time: "11:15" },
        { step: "Approval", completed: true, time: "Yesterday" }
      ],
      caseNumber: "IT150-2024-0002",
      estimatedCompletion: "Completed"
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved": return <CheckCircle className="w-5 h-5 text-sars-green" />;
      case "processing": return <Clock className="w-5 h-5 text-yellow-500" />;
      case "rejected": return <AlertCircle className="w-5 h-5 text-red-500" />;
      default: return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved": return "text-sars-green";
      case "processing": return "text-yellow-600";
      case "rejected": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-sars-blue text-white p-6">
        <h1 className="text-2xl font-bold">Submission Tracker</h1>
        <p className="text-blue-200">Track your BOE submissions</p>
      </div>

      <div className="p-4 space-y-6">
        {submissions.map((submission) => (
          <Card key={submission.id} className="overflow-hidden">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg text-sars-blue">{submission.id}</CardTitle>
                  <p className="text-sm text-gray-600">{submission.supplier}</p>
                </div>
                <div className="text-right">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(submission.status)}
                    <span className={`font-medium capitalize ${getStatusColor(submission.status)}`}>
                      {submission.status}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{submission.submittedDate}</p>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              {/* Timeline */}
              <div className="mb-6">
                <h3 className="font-medium mb-3">Status Timeline</h3>
                <div className="space-y-3">
                  {submission.timeline.map((item, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full flex-shrink-0 ${
                        item.completed ? 'bg-sars-green' : 'bg-gray-300'
                      }`} />
                      <div className="flex-1">
                        <span className={`text-sm ${
                          item.completed ? 'text-gray-900' : 'text-gray-500'
                        }`}>
                          {item.step}
                        </span>
                        <div className="text-xs text-gray-500">{item.time}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Case Information */}
              {submission.status !== "draft" && (
                <Card className="bg-sars-green/5 border-sars-green/20 mb-4">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-sars-green">Case Number Assigned</p>
                        <p className="text-lg font-bold text-sars-blue">{submission.caseNumber}</p>
                      </div>
                      <Button size="sm" variant="outline" className="border-sars-green text-sars-green">
                        <Download className="w-4 h-4 mr-1" />
                        Receipt
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Actions */}
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Eye className="w-4 h-4 mr-1" />
                  View Details
                </Button>
                {submission.status === "approved" && (
                  <Button variant="outline" size="sm" className="text-sars-green border-sars-green">
                    <Download className="w-4 h-4 mr-1" />
                    Download Certificate
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Quick Stats */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Stats</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-sars-blue">8</div>
                <div className="text-xs text-gray-600">Total Submitted</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-sars-green">6</div>
                <div className="text-xs text-gray-600">Approved</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-yellow-600">2</div>
                <div className="text-xs text-gray-600">Processing</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* New Submission Button */}
        <Button
          onClick={() => navigate('/capture')}
          className="w-full bg-sars-green hover:bg-green-600"
        >
          Create New BOE
        </Button>
      </div>

      <BottomNavigation currentPage="tracker" />
    </div>
  );
};

export default SubmissionTrackerScreen;
