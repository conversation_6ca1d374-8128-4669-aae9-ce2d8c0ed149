
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useNavigate } from "react-router-dom";
import { 
  ArrowLeft, 
  Plane, 
  Building, 
  MapPin, 
  Package, 
  AlertTriangle,
  Calculator,
  Eye,
  Save
} from "lucide-react";
import { useState } from "react";

const AWBCreationScreen = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 5;

  const steps = [
    "Flight & Shipper",
    "Consignment",
    "Dangerous Goods",
    "Charges",
    "Preview"
  ];

  const renderStepContent = () => {
    switch(currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            {/* Flight Selector */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sky-600 flex items-center gap-2">
                  <Plane className="w-5 h-5" />
                  Flight Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Airline Code</label>
                    <Input placeholder="SA, LH, EK..." />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Flight Number</label>
                    <Input placeholder="SA201" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Origin</label>
                    <Input placeholder="JNB" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Destination</label>
                    <Input placeholder="LHR" />
                  </div>
                </div>
                <div className="bg-sky-50 p-3 rounded-lg">
                  <div className="text-sm text-sky-700">
                    <strong>Aircraft:</strong> Boeing 737-800 • <strong>Capacity:</strong> 18.2 tons
                  </div>
                  <div className="w-full bg-sky-200 rounded-full h-2 mt-2">
                    <div className="bg-sky-500 h-2 rounded-full" style={{width: '65%'}}></div>
                  </div>
                  <div className="text-xs text-sky-600 mt-1">65% Capacity Used</div>
                </div>
              </CardContent>
            </Card>

            {/* Shipper Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sky-600 flex items-center gap-2">
                  <Building className="w-5 h-5" />
                  Shipper Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input placeholder="Company Name" />
                <Input placeholder="Address Line 1" />
                <Input placeholder="City, Country" />
                <div className="grid grid-cols-2 gap-4">
                  <Input placeholder="VAT Number" />
                  <Input placeholder="Contact Number" />
                </div>
                <div className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  <label className="text-sm">Same as BOE Consignee</label>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            {/* Package Builder */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sky-600 flex items-center gap-2">
                  <Package className="w-5 h-5" />
                  Consignment Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Length (cm)</label>
                    <Input placeholder="100" type="number" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Width (cm)</label>
                    <Input placeholder="80" type="number" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Height (cm)</label>
                    <Input placeholder="60" type="number" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Actual Weight (kg)</label>
                    <Input placeholder="48" type="number" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Pieces</label>
                    <Input placeholder="1" type="number" />
                  </div>
                </div>
                
                {/* Weight Calculator */}
                <div className="bg-sky-50 p-4 rounded-lg">
                  <h4 className="font-medium text-sky-700 mb-2 flex items-center gap-2">
                    <Calculator className="w-4 h-4" />
                    Weight Calculation
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Actual Weight:</span>
                      <span className="font-medium">48 kg</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Volumetric Weight:</span>
                      <span className="font-medium">72 kg</span>
                    </div>
                    <hr className="border-sky-200" />
                    <div className="flex justify-between text-sky-700 font-bold">
                      <span>Chargeable Weight:</span>
                      <span>72 kg</span>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Cargo Description</label>
                  <Input placeholder="Electronics - Mobile phones" />
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            {/* Dangerous Goods */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sky-600 flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5" />
                  Dangerous Goods Declaration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  <label className="text-sm">This shipment contains dangerous goods</label>
                </div>
                
                <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                  <h4 className="font-medium text-yellow-800 mb-2">Special Handling Codes</h4>
                  <div className="grid grid-cols-2 gap-2">
                    <label className="flex items-center gap-2">
                      <input type="checkbox" className="rounded" />
                      <span className="text-sm">[PER] Perishable</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" className="rounded" />
                      <span className="text-sm">[VAL] Valuables</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" className="rounded" />
                      <span className="text-sm">[DGR] Dangerous Goods</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" className="rounded" />
                      <span className="text-sm">[LIV] Live Animals</span>
                    </label>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">UN Number (if applicable)</label>
                  <Input placeholder="UN1950" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Emergency Contact</label>
                  <Input placeholder="+27 11 123 4567" />
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            {/* Charge Calculation */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sky-600 flex items-center gap-2">
                  <Calculator className="w-5 h-5" />
                  Rate Breakdown
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Rate per kg</label>
                    <Input placeholder="45.50" type="number" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Currency</label>
                    <select className="w-full p-2 border rounded-md">
                      <option>ZAR</option>
                      <option>USD</option>
                      <option>EUR</option>
                    </select>
                  </div>
                </div>

                <div className="bg-sky-50 p-4 rounded-lg">
                  <h4 className="font-medium text-sky-700 mb-3">Charge Summary</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Base Rate (72kg × R45.50):</span>
                      <span>R3,276.00</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Fuel Surcharge (12%):</span>
                      <span>R393.12</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Security Fee:</span>
                      <span>R125.00</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Handling Fee:</span>
                      <span>R85.00</span>
                    </div>
                    <hr className="border-sky-200" />
                    <div className="flex justify-between font-bold text-sky-700">
                      <span>Total Charges:</span>
                      <span>R3,879.12</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            {/* AWB Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sky-600 flex items-center gap-2">
                  <Eye className="w-5 h-5" />
                  AWB Preview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-white border-2 border-sky-200 p-4 rounded-lg">
                  <div className="text-center border-b pb-2 mb-4">
                    <h3 className="font-bold text-lg">AIR WAYBILL</h3>
                    <p className="text-sm text-gray-600">IATA Form 600b</p>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <strong>AWB Number:</strong> 180-12345678
                    </div>
                    <div>
                      <strong>Flight:</strong> SA201 JNB-LHR
                    </div>
                    <div>
                      <strong>Shipper:</strong> Tech Exports Ltd
                    </div>
                    <div>
                      <strong>Consignee:</strong> London Electronics
                    </div>
                    <div>
                      <strong>Weight:</strong> 72kg (Chargeable)
                    </div>
                    <div>
                      <strong>Pieces:</strong> 1
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Total Charges:</span>
                      <span className="font-bold text-lg">R3,879.12</span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-4">
                  <Button variant="outline" className="flex-1">
                    Save as Draft
                  </Button>
                  <Button className="flex-1 bg-sky-500 hover:bg-sky-600">
                    Generate AWB
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-sky-500 text-white p-6">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/awb')}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div className="flex-1">
            <h1 className="text-xl font-bold">Create AWB</h1>
            <p className="text-sky-200">{steps[currentStep - 1]}</p>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-sky-600 rounded-full h-2">
          <div 
            className="bg-white h-2 rounded-full transition-all duration-300" 
            style={{width: `${(currentStep / totalSteps) * 100}%`}}
          ></div>
        </div>
        <div className="text-xs text-sky-200 mt-1">
          Step {currentStep} of {totalSteps}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {renderStepContent()}

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
            disabled={currentStep === 1}
          >
            Previous
          </Button>
          <Button
            onClick={() => {
              if (currentStep === totalSteps) {
                navigate('/awb');
              } else {
                setCurrentStep(Math.min(totalSteps, currentStep + 1));
              }
            }}
            className="bg-sky-500 hover:bg-sky-600"
          >
            {currentStep === totalSteps ? 'Complete' : 'Next'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AWBCreationScreen;
