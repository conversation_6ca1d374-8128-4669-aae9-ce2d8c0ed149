
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { FileText, Package, ArrowLeft, Plane, Anchor, Truck } from "lucide-react";

const DocumentSelectionScreen = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-sars-blue text-white p-6">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/capture')}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold">Choose Document Type</h1>
            <p className="text-blue-200">What would you like to generate?</p>
          </div>
        </div>
      </div>

      {/* Document Type Selection */}
      <div className="flex-1 p-6 flex items-center justify-center">
        <div className="w-full max-w-md space-y-6">
          <Card className="cursor-pointer hover:shadow-lg transition-all border-2 hover:border-sars-blue">
            <CardContent className="p-8 text-center" onClick={() => navigate('/verification')}>
              <FileText className="w-16 h-16 mx-auto text-sars-blue mb-4" />
              <h3 className="text-xl font-bold text-sars-blue mb-2">Bill of Entry (BOE)</h3>
              <p className="text-gray-600 text-sm mb-4">
                Individual customs declaration for imported goods
              </p>
              <div className="text-xs text-gray-500">
                ✓ Single supplier invoice<br />
                ✓ Direct customs clearance<br />
                ✓ Individual item processing
              </div>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-all border-2 hover:border-sky-500">
            <CardContent className="p-8 text-center" onClick={() => navigate('/awb/create')}>
              <Plane className="w-16 h-16 mx-auto text-sky-500 mb-4" />
              <h3 className="text-xl font-bold text-sky-500 mb-2">Air Waybill (AWB)</h3>
              <p className="text-gray-600 text-sm mb-4">
                Air cargo shipping document for freight transport
              </p>
              <div className="text-xs text-gray-500">
                ✓ International air freight<br />
                ✓ IATA compliant format<br />
                ✓ Real-time flight tracking
              </div>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-all border-2 hover:border-deep-sea-blue">
            <CardContent className="p-8 text-center" onClick={() => navigate('/bol/create')}>
              <Anchor className="w-16 h-16 mx-auto text-deep-sea-blue mb-4" />
              <h3 className="text-xl font-bold text-deep-sea-blue mb-2">Bill of Lading (BOL)</h3>
              <p className="text-gray-600 text-sm mb-4">
                Ocean freight shipping document for sea cargo
              </p>
              <div className="text-xs text-gray-500">
                ✓ Maritime cargo transport<br />
                ✓ Container management<br />
                ✓ Voyage tracking
              </div>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-all border-2 hover:border-terracotta">
            <CardContent className="p-8 text-center" onClick={() => navigate('/rfm/create')}>
              <Truck className="w-16 h-16 mx-auto text-terracotta mb-4" />
              <h3 className="text-xl font-bold text-terracotta mb-2">Road Freight Manifest (RFM)</h3>
              <p className="text-gray-600 text-sm mb-4">
                Cross-border road transport documentation
              </p>
              <div className="text-xs text-gray-500">
                ✓ SADC road transport<br />
                ✓ Border compliance checks<br />
                ✓ Journey tracking
              </div>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-all border-2 hover:border-teal-600">
            <CardContent className="p-8 text-center" onClick={() => navigate('/manifest/create')}>
              <Package className="w-16 h-16 mx-auto text-teal-600 mb-4" />
              <h3 className="text-xl font-bold text-teal-600 mb-2">Manifest</h3>
              <p className="text-gray-600 text-sm mb-4">
                Consolidated shipping document for multiple items
              </p>
              <div className="text-xs text-gray-500">
                ✓ Multiple item consolidation<br />
                ✓ Container/cargo management<br />
                ✓ Bulk processing
              </div>
            </CardContent>
          </Card>

          <div className="text-center text-gray-500 text-sm">
            Not sure? <Button variant="link" className="text-sars-blue p-0 h-auto">Learn more about document types</Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentSelectionScreen;
