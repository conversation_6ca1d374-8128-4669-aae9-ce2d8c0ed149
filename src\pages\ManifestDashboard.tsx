
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { Package, Plus, FileText, Upload, BookOpen, TrendingUp, Clock, CheckCircle } from "lucide-react";
import BottomNavigation from "@/components/BottomNavigation";

const ManifestDashboard = () => {
  const navigate = useNavigate();

  const recentActivity = [
    { id: 1, action: "Updated Container #MSKU48271", detail: "Added 5 BOEs", time: "2 hours ago" },
    { id: 2, action: "Manifest MAN-2024-007", detail: "Submitted to SARS", time: "5 hours ago" },
    { id: 3, action: "Container #TCLU89043", detail: "Compliance check passed", time: "1 day ago" },
  ];

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-gradient-to-r from-sars-blue to-teal-600 text-white p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Manifest Center</h1>
            <p className="text-blue-200">Manage customs manifests & containers</p>
          </div>
          <Package className="w-8 h-8 text-white/80" />
        </div>

        {/* Status Summary Cards */}
        <div className="grid grid-cols-3 gap-4">
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">3</div>
              <div className="text-xs text-blue-200">Draft Manifests</div>
            </CardContent>
          </Card>
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">2</div>
              <div className="text-xs text-blue-200">Pending Submissions</div>
            </CardContent>
          </Card>
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">12</div>
              <div className="text-xs text-blue-200">SARS Approved</div>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="p-6">
        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-3 gap-4">
            <Button
              onClick={() => navigate('/manifest/create')}
              className="h-20 flex flex-col bg-teal-600 hover:bg-teal-700 text-white"
            >
              <Plus className="w-6 h-6 mb-1" />
              <span className="text-xs">New Manifest</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col border-teal-600 text-teal-600 hover:bg-teal-600 hover:text-white"
            >
              <Upload className="w-6 h-6 mb-1" />
              <span className="text-xs">Import BOEs</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col border-gray-300 text-gray-600 hover:bg-gray-100"
            >
              <BookOpen className="w-6 h-6 mb-1" />
              <span className="text-xs">Templates</span>
            </Button>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
          <div className="space-y-3">
            {recentActivity.map((activity) => (
              <Card key={activity.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <p className="font-medium text-sars-blue">{activity.action}</p>
                      <p className="text-sm text-gray-600">{activity.detail}</p>
                    </div>
                    <div className="text-xs text-gray-500">{activity.time}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Current Manifests */}
        <div>
          <h2 className="text-lg font-semibold mb-4">Current Manifests</h2>
          <div className="space-y-3">
            <Card className="border-teal-200 bg-teal-50">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-teal-800">MAN-2024-008</span>
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4 text-yellow-500" />
                    <span className="text-sm text-yellow-600">Draft</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-2">Container MSKU48271 • 8 BOEs • R 245,800</p>
                <div className="flex gap-2">
                  <Button size="sm" className="bg-teal-600 hover:bg-teal-700">Continue</Button>
                  <Button size="sm" variant="outline">Preview</Button>
                </div>
              </CardContent>
            </Card>

            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-green-800">MAN-2024-007</span>
                  <div className="flex items-center gap-1">
                    <CheckCircle className="w-4 h-4 text-sars-green" />
                    <span className="text-sm text-sars-green">Approved</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-2">Container TCLU89043 • 12 BOEs • R 487,200</p>
                <Button size="sm" variant="outline" className="border-sars-green text-sars-green">
                  Download Certificate
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Floating Action Button */}
      <Button
        onClick={() => navigate('/manifest/create')}
        className="fixed bottom-24 right-6 w-14 h-14 rounded-full bg-teal-600 hover:bg-teal-700 shadow-lg"
        size="sm"
      >
        <Plus className="w-6 h-6" />
      </Button>

      <BottomNavigation currentPage="manifest" />
    </div>
  );
};

export default ManifestDashboard;
