
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Scan, MapPin, Calendar, Ship, Plane, Truck } from "lucide-react";

const ManifestSetupScreen = () => {
  const [carrierType, setCarrierType] = useState<"sea" | "air" | "road">("sea");
  const navigate = useNavigate();

  const carrierTypes = [
    { id: "sea", label: "Sea Freight", icon: Ship, color: "bg-blue-500" },
    { id: "air", label: "Air Freight", icon: Plane, color: "bg-sky-500" },
    { id: "road", label: "Road Transport", icon: Truck, color: "bg-green-500" },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-teal-600 text-white p-6">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/manifest')}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold">New Manifest</h1>
            <p className="text-teal-200">Step 1 of 5 - Setup</p>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-white/20 rounded-full h-2">
          <div className="bg-white h-2 rounded-full w-1/5"></div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Carrier Type Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="text-teal-800">1. Carrier Information</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">Select transport mode</p>
            <div className="grid grid-cols-1 gap-3">
              {carrierTypes.map((type) => {
                const Icon = type.icon;
                return (
                  <button
                    key={type.id}
                    onClick={() => setCarrierType(type.id as any)}
                    className={`flex items-center gap-3 p-4 rounded-lg border-2 transition-all ${
                      carrierType === type.id
                        ? 'border-teal-600 bg-teal-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className={`w-10 h-10 rounded-full ${type.color} flex items-center justify-center`}>
                      <Icon className="w-5 h-5 text-white" />
                    </div>
                    <span className="font-medium">{type.label}</span>
                  </button>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Carrier ID Scanner */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <p className="font-medium">Carrier ID</p>
                <p className="text-sm text-gray-600">
                  {carrierType === 'air' ? 'IATA Code' : carrierType === 'sea' ? 'IMO Number' : 'Vehicle Registration'}
                </p>
              </div>
              <Button variant="outline" size="sm">
                <Scan className="w-4 h-4 mr-2" />
                Scan
              </Button>
            </div>
            <input
              type="text"
              placeholder={carrierType === 'air' ? 'e.g., SA123' : carrierType === 'sea' ? 'e.g., 9123456' : 'e.g., ABC123GP'}
              className="w-full p-3 border border-gray-300 rounded-lg focus:border-teal-600 focus:outline-none"
            />
          </CardContent>
        </Card>

        {/* Transport Details */}
        <Card>
          <CardHeader>
            <CardTitle className="text-teal-800">2. Transport Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                {carrierType === 'air' ? 'Flight Number' : carrierType === 'sea' ? 'Voyage Number' : 'Trip Reference'}
              </label>
              <input
                type="text"
                placeholder={carrierType === 'air' ? 'SA123' : carrierType === 'sea' ? 'VOY001' : 'TRIP001'}
                className="w-full p-3 border border-gray-300 rounded-lg focus:border-teal-600 focus:outline-none"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                {carrierType === 'air' ? 'Aircraft Registration' : carrierType === 'sea' ? 'Vessel Name' : 'Vehicle ID'}
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  placeholder={carrierType === 'air' ? 'ZS-ABC' : carrierType === 'sea' ? 'MSC CONTAINER' : 'ABC123GP'}
                  className="flex-1 p-3 border border-gray-300 rounded-lg focus:border-teal-600 focus:outline-none"
                />
                <Button variant="outline" size="sm">
                  <Scan className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Departure Date</label>
                <div className="relative">
                  <input
                    type="date"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:border-teal-600 focus:outline-none"
                  />
                  <Calendar className="absolute right-3 top-3 w-5 h-5 text-gray-400 pointer-events-none" />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Arrival Date</label>
                <div className="relative">
                  <input
                    type="date"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:border-teal-600 focus:outline-none"
                  />
                  <Calendar className="absolute right-3 top-3 w-5 h-5 text-gray-400 pointer-events-none" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Route Mapping */}
        <Card>
          <CardHeader>
            <CardTitle className="text-teal-800">3. Route Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Port of Loading</label>
              <div className="flex gap-2">
                <input
                  type="text"
                  placeholder="Search port or city..."
                  className="flex-1 p-3 border border-gray-300 rounded-lg focus:border-teal-600 focus:outline-none"
                />
                <Button variant="outline" size="sm">
                  <MapPin className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Port of Discharge</label>
              <div className="flex gap-2">
                <input
                  type="text"
                  placeholder="Durban Port, South Africa"
                  className="flex-1 p-3 border border-gray-300 rounded-lg focus:border-teal-600 focus:outline-none"
                />
                <Button variant="outline" size="sm">
                  <MapPin className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Customs Office</label>
              <select className="w-full p-3 border border-gray-300 rounded-lg focus:border-teal-600 focus:outline-none">
                <option>Durban Harbour Customs</option>
                <option>OR Tambo International</option>
                <option>Cape Town Port</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex gap-4 pt-4">
          <Button variant="outline" className="flex-1" onClick={() => navigate('/manifest')}>
            Save Draft
          </Button>
          <Button className="flex-1 bg-teal-600 hover:bg-teal-700" onClick={() => navigate('/manifest/containers')}>
            Continue
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ManifestSetupScreen;
