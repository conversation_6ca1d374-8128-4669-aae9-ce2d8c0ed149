import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import OnboardingScreen from "./pages/OnboardingScreen";
import DashboardScreen from "./pages/DashboardScreen";
import BOEDashboard from "./pages/BOEDashboard";
import InvoiceCaptureScreen from "./pages/InvoiceCaptureScreen";
import DocumentSelectionScreen from "./pages/DocumentSelectionScreen";
import OCRProcessingScreen from "./pages/OCRProcessingScreen";
import DataVerificationScreen from "./pages/DataVerificationScreen";
import BOEGeneratorScreen from "./pages/BOEGeneratorScreen";
import ReviewSubmitScreen from "./pages/ReviewSubmitScreen";
import SubmissionTrackerScreen from "./pages/SubmissionTrackerScreen";
import ManifestDashboard from "./pages/ManifestDashboard";
import ManifestSetupScreen from "./pages/ManifestSetupScreen";
import ContainerManagementScreen from "./pages/ContainerManagementScreen";
import AWBDashboard from "./pages/AWBDashboard";
import AWBCreationScreen from "./pages/AWBCreationScreen";
import FlightTrackerScreen from "./pages/FlightTrackerScreen";
import BOLDashboard from "./pages/BOLDashboard";
import BOLCreationScreen from "./pages/BOLCreationScreen";
import VoyageTrackerScreen from "./pages/VoyageTrackerScreen";
import RFMDashboard from "./pages/RFMDashboard";
import RFMCreationScreen from "./pages/RFMCreationScreen";
import JourneyTrackerScreen from "./pages/JourneyTrackerScreen";
import NotFound from "./pages/NotFound";
import ProfileScreen from "./pages/ProfileScreen";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <div className="font-roboto min-h-screen bg-gray-50">
          <Routes>
            <Route path="/" element={<OnboardingScreen />} />
            <Route path="/dashboard" element={<DashboardScreen />} />
            <Route path="/profile" element={<ProfileScreen />} />
            <Route path="/boe" element={<BOEDashboard />} />
            <Route path="/capture" element={<InvoiceCaptureScreen />} />
            <Route path="/document-selection" element={<DocumentSelectionScreen />} />
            <Route path="/processing" element={<OCRProcessingScreen />} />
            <Route path="/verification" element={<DataVerificationScreen />} />
            <Route path="/generator" element={<BOEGeneratorScreen />} />
            <Route path="/review" element={<ReviewSubmitScreen />} />
            <Route path="/tracker" element={<SubmissionTrackerScreen />} />
            <Route path="/manifest" element={<ManifestDashboard />} />
            <Route path="/manifest/create" element={<ManifestSetupScreen />} />
            <Route path="/manifest/containers" element={<ContainerManagementScreen />} />
            <Route path="/awb" element={<AWBDashboard />} />
            <Route path="/awb/create" element={<AWBCreationScreen />} />
            <Route path="/awb/tracker" element={<FlightTrackerScreen />} />
            <Route path="/bol" element={<BOLDashboard />} />
            <Route path="/bol/create" element={<BOLCreationScreen />} />
            <Route path="/bol/tracker" element={<VoyageTrackerScreen />} />
            <Route path="/rfm" element={<RFMDashboard />} />
            <Route path="/rfm/create" element={<RFMCreationScreen />} />
            <Route path="/rfm/tracker" element={<JourneyTrackerScreen />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </div>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
