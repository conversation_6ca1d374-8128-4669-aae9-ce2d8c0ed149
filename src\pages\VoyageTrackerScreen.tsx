
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Ship, Anchor, MapPin, Clock, Waves, Navigation } from "lucide-react";
import BottomNavigation from "@/components/BottomNavigation";

const VoyageTrackerScreen = () => {
  const navigate = useNavigate();

  const voyageData = {
    vessel: "MSC OSCAR",
    voyage: "VY240115",
    bol: "BOL-2024-001",
    currentLocation: "Indian Ocean",
    nextPort: "Singapore",
    eta: "2024-01-22 14:30",
    progress: 65
  };

  const portRotation = [
    { port: "Cape Town", country: "South Africa", status: "departed", date: "2024-01-15 18:00", current: false },
    { port: "Durban", country: "South Africa", status: "departed", date: "2024-01-17 12:00", current: false },
    { port: "Indian Ocean", country: "En Route", status: "current", date: "In Transit", current: true },
    { port: "Singapore", country: "Singapore", status: "upcoming", date: "2024-01-22 14:30", current: false },
    { port: "Shanghai", country: "China", status: "upcoming", date: "2024-01-25 08:00", current: false },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "departed": return "text-gray-500";
      case "current": return "text-deep-sea-blue";
      case "upcoming": return "text-yellow-600";
      default: return "text-gray-500";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "departed": return "✓";
      case "current": return "📍";
      case "upcoming": return "⏳";
      default: return "○";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-gradient-to-r from-deep-sea-blue to-blue-800 text-white p-6 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Waves className="w-full h-full" />
        </div>
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/bol')}
              className="text-white hover:bg-white/10"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold flex items-center gap-2">
                <Navigation className="w-6 h-6" />
                Voyage Tracker
              </h1>
              <p className="text-blue-200">{voyageData.vessel} - {voyageData.voyage}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Current Status */}
      <div className="p-6">
        <Card className="border-deep-sea-blue/20 bg-deep-sea-blue/5 mb-6">
          <CardHeader>
            <CardTitle className="text-deep-sea-blue flex items-center gap-2">
              <Ship className="w-5 h-5" />
              Current Voyage Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <div className="text-sm text-gray-600">Current Location</div>
                <div className="font-semibold text-deep-sea-blue">{voyageData.currentLocation}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Next Port</div>
                <div className="font-semibold">{voyageData.nextPort}</div>
              </div>
            </div>
            
            <div className="mb-4">
              <div className="flex justify-between text-sm mb-2">
                <span>Voyage Progress</span>
                <span>{voyageData.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-deep-sea-blue h-3 rounded-full transition-all duration-300"
                  style={{ width: `${voyageData.progress}%` }}
                />
              </div>
            </div>

            <div className="flex items-center gap-2 text-sm">
              <Clock className="w-4 h-4 text-deep-sea-blue" />
              <span>ETA Singapore: {voyageData.eta}</span>
            </div>
          </CardContent>
        </Card>

        {/* Port Rotation */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Anchor className="w-5 h-5 text-deep-sea-blue" />
              Port Rotation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {portRotation.map((port, index) => (
                <div key={index} className="flex items-center gap-4">
                  <div className={`text-2xl ${getStatusColor(port.status)}`}>
                    {getStatusIcon(port.status)}
                  </div>
                  <div className="flex-1">
                    <div className={`font-semibold ${getStatusColor(port.status)}`}>
                      {port.port}
                    </div>
                    <div className="text-sm text-gray-600">{port.country}</div>
                    <div className="text-xs text-gray-500">{port.date}</div>
                  </div>
                  {port.current && (
                    <div className="px-2 py-1 bg-deep-sea-blue text-white text-xs rounded-full">
                      Current
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* BOL Information */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-deep-sea-blue">Bill of Lading Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">BOL Number:</span>
                <span className="font-semibold">{voyageData.bol}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Vessel:</span>
                <span>{voyageData.vessel}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Voyage:</span>
                <span>{voyageData.voyage}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Container:</span>
                <span>MSKU1234567</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Live Updates */}
        <Card className="border-deep-sea-blue/20 bg-deep-sea-blue/5">
          <CardHeader>
            <CardTitle className="text-deep-sea-blue text-sm flex items-center gap-2">
              <Waves className="w-4 h-4" />
              Live Maritime Updates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-deep-sea-blue rounded-full mt-2"></div>
                <div>
                  <div className="font-semibold">15:30 - Weather Update</div>
                  <div className="text-gray-600">Favorable weather conditions. Maintaining scheduled speed.</div>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                <div>
                  <div className="font-semibold">12:00 - Port Advisory</div>
                  <div className="text-gray-600">Singapore port reports normal operations. No delays expected.</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="mt-6 space-y-3">
          <Button 
            onClick={() => navigate('/bol')}
            className="w-full bg-deep-sea-blue hover:bg-blue-700"
          >
            View BOL Details
          </Button>
          <Button 
            variant="outline" 
            className="w-full border-deep-sea-blue text-deep-sea-blue"
          >
            Download Tracking Report
          </Button>
        </div>
      </div>

      <BottomNavigation currentPage="bol" />
    </div>
  );
};

export default VoyageTrackerScreen;
