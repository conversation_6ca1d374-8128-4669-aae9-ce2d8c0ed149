
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { 
  ArrowLeft, 
  Plane, 
  MapPin, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Package,
  Users,
  Gauge
} from "lucide-react";

const FlightTrackerScreen = () => {
  const navigate = useNavigate();

  const flights = [
    {
      id: "SA201",
      airline: "SAA",
      route: "JNB → LHR",
      status: "In Transit",
      departure: "14:30",
      arrival: "06:45+1",
      awbCount: 12,
      progress: 65
    },
    {
      id: "LH572", 
      airline: "Lufthansa",
      route: "CPT → FRA",
      status: "Boarding",
      departure: "16:20",
      arrival: "07:15+1", 
      awbCount: 8,
      progress: 5
    },
    {
      id: "EK761",
      airline: "Emirates", 
      route: "JNB → DXB",
      status: "Arrived",
      departure: "10:15",
      arrival: "18:45",
      awbCount: 15,
      progress: 100
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Arrived": return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "In Transit": return <Plane className="w-4 h-4 text-sky-500" />;
      case "Boarding": return <Clock className="w-4 h-4 text-yellow-500" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Arrived": return "text-green-600 bg-green-50";
      case "In Transit": return "text-sky-600 bg-sky-50";
      case "Boarding": return "text-yellow-600 bg-yellow-50";
      default: return "text-gray-600 bg-gray-50";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-sky-500 text-white p-6">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/awb')}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold">Flight Tracker</h1>
            <p className="text-sky-200">Real-time cargo flight monitoring</p>
          </div>
        </div>
      </div>

      {/* Live Stats */}
      <div className="p-6">
        <div className="grid grid-cols-3 gap-4 mb-6">
          <Card className="bg-sky-50 border-sky-200">
            <CardContent className="p-4 text-center">
              <Plane className="w-6 h-6 mx-auto text-sky-600 mb-2" />
              <div className="text-xl font-bold text-sky-600">7</div>
              <div className="text-xs text-sky-700">Active Flights</div>
            </CardContent>
          </Card>
          <Card className="bg-green-50 border-green-200">
            <CardContent className="p-4 text-center">
              <Package className="w-6 h-6 mx-auto text-green-600 mb-2" />
              <div className="text-xl font-bold text-green-600">42</div>
              <div className="text-xs text-green-700">AWBs in Transit</div>
            </CardContent>
          </Card>
          <Card className="bg-yellow-50 border-yellow-200">
            <CardContent className="p-4 text-center">
              <Clock className="w-6 h-6 mx-auto text-yellow-600 mb-2" />
              <div className="text-xl font-bold text-yellow-600">18hr</div>
              <div className="text-xs text-yellow-700">Avg. Transit Time</div>
            </CardContent>
          </Card>
        </div>

        {/* Flight List */}
        <h2 className="text-lg font-semibold mb-4">Tracked Flights</h2>
        <div className="space-y-4">
          {flights.map((flight) => (
            <Card key={flight.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-sky-100 rounded-full flex items-center justify-center">
                      <Plane className="w-5 h-5 text-sky-600" />
                    </div>
                    <div>
                      <div className="font-semibold">{flight.id}</div>
                      <div className="text-sm text-gray-600">{flight.airline}</div>
                    </div>
                  </div>
                  <div className={`px-3 py-1 rounded-full text-xs flex items-center gap-1 ${getStatusColor(flight.status)}`}>
                    {getStatusIcon(flight.status)}
                    {flight.status}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-3">
                  <div>
                    <div className="text-sm text-gray-500">Route</div>
                    <div className="font-medium">{flight.route}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">AWBs</div>
                    <div className="font-medium flex items-center gap-1">
                      <Package className="w-4 h-4" />
                      {flight.awbCount}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-3">
                  <div>
                    <div className="text-sm text-gray-500">Departure</div>
                    <div className="font-medium">{flight.departure}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Arrival</div>
                    <div className="font-medium">{flight.arrival}</div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mb-2">
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-500">Flight Progress</span>
                    <span className="text-sky-600">{flight.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-sky-500 h-2 rounded-full transition-all duration-300" 
                      style={{width: `${flight.progress}%`}}
                    ></div>
                  </div>
                </div>

                <div className="flex gap-2 mt-3">
                  <Button variant="outline" size="sm" className="flex-1">
                    <MapPin className="w-4 h-4 mr-1" />
                    Track Live
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Package className="w-4 h-4 mr-1" />
                    View AWBs
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Airport Status */}
        <Card className="mt-6 border-sky-200 bg-sky-50">
          <CardHeader>
            <CardTitle className="text-sky-600 text-sm flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              Airport Status Updates
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>OR Tambo: Normal operations</span>
              </div>
              <div className="flex items-center gap-2">
                <AlertCircle className="w-4 h-4 text-yellow-500" />
                <span>Cape Town: 30min cargo delays due to weather</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>King Shaka: All cargo operations normal</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default FlightTrackerScreen;
