
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useNavigate } from "react-router-dom";
import { 
  ArrowLeft, 
  User, 
  Settings, 
  Bell, 
  Shield, 
  HelpCircle, 
  LogOut,
  FileText,
  Package,
  Plane,
  Anchor,
  Truck,
  CheckCircle,
  Home
} from "lucide-react";

const ProfileScreen = () => {
  const navigate = useNavigate();

  const userStats = [
    { label: "BOE Documents", value: "18", icon: FileText, color: "text-sars-blue" },
    { label: "Manifests", value: "7", icon: Package, color: "text-teal-600" },
    { label: "Air Waybills", value: "12", icon: Plane, color: "text-sky-500" },
    { label: "Bills of Lading", value: "5", icon: Anchor, color: "text-deep-sea-blue" },
    { label: "RFM Documents", value: "9", icon: Truck, color: "text-terracotta" },
  ];

  const menuItems = [
    { icon: Settings, label: "Account Settings", action: () => {} },
    { icon: Bell, label: "Notifications", action: () => {} },
    { icon: Shield, label: "Security", action: () => {} },
    { icon: HelpCircle, label: "Help & Support", action: () => {} },
    { icon: LogOut, label: "Sign Out", action: () => {}, danger: true },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-sars-blue to-teal-600 text-white p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/dashboard')}
              className="text-white hover:bg-white/10"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <h1 className="text-xl font-bold">Profile</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
              <Bell className="w-5 h-5" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-white hover:bg-white/10"
              onClick={() => navigate('/dashboard')}
            >
              <Home className="w-5 h-5" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-white hover:bg-white/10 bg-white/20"
              onClick={() => navigate('/profile')}
            >
              <User className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* User Info */}
        <div className="flex items-center gap-4">
          <Avatar className="w-16 h-16">
            <AvatarImage src="/placeholder.svg" />
            <AvatarFallback className="bg-white/20 text-white text-lg font-bold">
              JD
            </AvatarFallback>
          </Avatar>
          <div>
            <h2 className="text-xl font-bold">John Doe</h2>
            <p className="text-blue-200"><EMAIL></p>
            <div className="flex items-center gap-2 mt-1">
              <CheckCircle className="w-4 h-4 text-sars-green" />
              <span className="text-sm text-blue-200">Verified Account</span>
            </div>
          </div>
        </div>
      </div>

      {/* Document Statistics */}
      <div className="p-6">
        <h3 className="text-lg font-semibold mb-4">Document Overview</h3>
        <div className="grid grid-cols-2 gap-4 mb-8">
          {userStats.map((stat) => {
            const Icon = stat.icon;
            return (
              <Card key={stat.label}>
                <CardContent className="p-4 text-center">
                  <Icon className={`w-8 h-8 mx-auto mb-2 ${stat.color}`} />
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <div className="text-xs text-gray-600">{stat.label}</div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Account Details */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">Account Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Company</span>
              <span className="font-medium">ABC Trading (Pty) Ltd</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Tax Number</span>
              <span className="font-medium">**********</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">SARS Client Code</span>
              <span className="font-medium">CC123456</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Member Since</span>
              <span className="font-medium">January 2023</span>
            </div>
          </CardContent>
        </Card>

        {/* Menu Items */}
        <div className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            return (
              <Card key={item.label} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Icon className={`w-5 h-5 ${item.danger ? 'text-red-500' : 'text-gray-600'}`} />
                      <span className={item.danger ? 'text-red-600' : 'text-gray-900'}>{item.label}</span>
                    </div>
                    <div className="text-gray-400">›</div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ProfileScreen;
