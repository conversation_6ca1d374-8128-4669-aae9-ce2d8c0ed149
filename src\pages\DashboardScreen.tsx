
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { 
  Scan, 
  FileText, 
  History, 
  Bell, 
  Plus, 
  TrendingUp, 
  CheckCircle, 
  Clock, 
  Package,
  Plane,
  Anchor,
  Truck,
  User,
  Search,
  Home
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import BottomNavigation from "@/components/BottomNavigation";

const DashboardScreen = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");

  const recentActivity = [
    { id: "BOE-2024-001", type: "BOE", supplier: "Tech Suppliers Ltd", status: "Approved", date: "2024-01-15" },
    { id: "MAN-2024-007", type: "Manifest", detail: "Container TCLU89043", status: "Submitted", date: "2024-01-14" },
    { id: "AWB-2024-003", type: "AWB", detail: "Flight SA201 - JNB→LHR", status: "In Transit", date: "2024-01-14" },
    { id: "BOL-2024-004", type: "BOL", detail: "Vessel MSC Diana", status: "Loading", date: "2024-01-13" },
    { id: "RFM-2024-015", type: "RFM", detail: "Route CPT→JHB→HRE", status: "In Transit", date: "2024-01-13" },
    { id: "BOE-2024-002", type: "BOE", supplier: "Import Express SA", status: "Processing", date: "2024-01-13" },
    { id: "MAN-2024-008", type: "Manifest", detail: "Container MSKU48271", status: "Draft", date: "2024-01-12" },
  ];

  const filteredActivity = recentActivity.filter(item =>
    item.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (item.supplier && item.supplier.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (item.detail && item.detail.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Approved": 
      case "Submitted": 
      case "Loading":
      case "In Transit": return <CheckCircle className="w-4 h-4 text-sars-green" />;
      case "Processing": return <Clock className="w-4 h-4 text-yellow-500" />;
      default: return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Approved":
      case "Submitted":
      case "Loading":
      case "In Transit": return "text-sars-green";
      case "Processing": return "text-yellow-600";
      default: return "text-gray-600";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "BOE": return "text-sars-blue";
      case "Manifest": return "text-teal-600";
      case "AWB": return "text-sky-500";
      case "BOL": return "text-deep-sea-blue";
      case "RFM": return "text-terracotta";
      default: return "text-gray-600";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "BOE": return <FileText className="w-4 h-4" />;
      case "Manifest": return <Package className="w-4 h-4" />;
      case "AWB": return <Plane className="w-4 h-4" />;
      case "BOL": return <Anchor className="w-4 h-4" />;
      case "RFM": return <Truck className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-gradient-to-r from-sars-blue to-teal-600 text-white p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Freight Assistant</h1>
            <p className="text-blue-200">Manage all your SARS documents</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
              <Bell className="w-5 h-5" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-white hover:bg-white/10"
              onClick={() => navigate('/dashboard')}
            >
              <Home className="w-5 h-5" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-white hover:bg-white/10"
              onClick={() => navigate('/profile')}
            >
              <User className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Stats Card */}
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardContent className="p-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">51</div>
                <div className="text-xs text-blue-200">Total Documents</div>
              </div>
              <div>
                <div className="text-2xl font-bold">89%</div>
                <div className="text-xs text-blue-200">Success Rate</div>
              </div>
              <div>
                <div className="text-2xl font-bold flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-sars-green mr-1" />
                  15
                </div>
                <div className="text-xs text-blue-200">This Month</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="p-6">
        <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-3 gap-3 mb-8">
          <Button
            onClick={() => navigate('/capture')}
            className="h-20 flex flex-col bg-sars-green hover:bg-green-600"
          >
            <Scan className="w-6 h-6 mb-1" />
            <span className="text-xs">Scan Document</span>
          </Button>
          <Button
            onClick={() => navigate('/boe')}
            variant="outline"
            className="h-20 flex flex-col border-sars-blue text-sars-blue hover:bg-sars-blue hover:text-white"
          >
            <FileText className="w-6 h-6 mb-1" />
            <span className="text-xs">BOE Center</span>
          </Button>
          <Button
            onClick={() => navigate('/manifest')}
            variant="outline"
            className="h-20 flex flex-col border-teal-600 text-teal-600 hover:bg-teal-600 hover:text-white"
          >
            <Package className="w-6 h-6 mb-1" />
            <span className="text-xs">Manifest</span>
          </Button>
        </div>

        {/* Document Type Centers */}
        <div className="grid grid-cols-3 gap-3 mb-8">
          <Button
            onClick={() => navigate('/awb')}
            variant="outline"
            className="h-16 flex flex-col items-center justify-center gap-1 border-sky-500 text-sky-500 hover:bg-sky-500 hover:text-white"
          >
            <Plane className="w-5 h-5" />
            <span className="text-xs text-center">Air Waybill</span>
          </Button>
          <Button
            onClick={() => navigate('/bol')}
            variant="outline"
            className="h-16 flex flex-col items-center justify-center gap-1 border-deep-sea-blue text-deep-sea-blue hover:bg-deep-sea-blue hover:text-white"
          >
            <Anchor className="w-5 h-5" />
            <span className="text-xs text-center">Bill of Lading</span>
          </Button>
          <Button
            onClick={() => navigate('/rfm')}
            variant="outline"
            className="h-16 flex flex-col items-center justify-center gap-1 border-terracotta text-terracotta hover:bg-terracotta hover:text-white"
          >
            <Truck className="w-5 h-5" />
            <span className="text-xs text-center">Road Freight Manifest</span>
          </Button>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search documents by ID, type, or supplier..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Recent Activity */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
          <div className="space-y-3">
            {filteredActivity.map((item) => (
              <Card key={item.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className={`font-medium ${getTypeColor(item.type)}`}>{item.id}</span>
                        <span className={`text-xs px-2 py-1 rounded-full flex items-center gap-1 ${
                          item.type === "BOE" ? "bg-blue-100 text-sars-blue" :
                          item.type === "Manifest" ? "bg-teal-100 text-teal-600" :
                          item.type === "AWB" ? "bg-sky-100 text-sky-500" :
                          item.type === "BOL" ? "bg-blue-100 text-deep-sea-blue" :
                          item.type === "RFM" ? "bg-orange-100 text-terracotta" :
                          "bg-gray-100 text-gray-600"
                        }`}>
                          {getTypeIcon(item.type)}
                          {item.type}
                        </span>
                        {getStatusIcon(item.status)}
                      </div>
                      <p className="text-sm text-gray-600">
                        {item.supplier || item.detail}
                      </p>
                      <p className="text-xs text-gray-500">{item.date}</p>
                    </div>
                    <div className={`text-sm font-medium ${getStatusColor(item.status)}`}>
                      {item.status}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* SARS Updates */}
        <Card className="border-sars-green/20 bg-sars-green/5">
          <CardHeader className="pb-3">
            <CardTitle className="text-sars-green text-sm">SARS Updates</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-sm text-gray-700">
              New exchange rates published for January 2024. Please ensure all document calculations use the updated rates.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Floating Action Button */}
      <Button
        onClick={() => navigate('/capture')}
        className="fixed bottom-24 right-6 w-14 h-14 rounded-full bg-sars-green hover:bg-green-600 shadow-lg animate-pulse-glow"
        size="sm"
      >
        <Plus className="w-6 h-6" />
      </Button>

      <BottomNavigation currentPage="dashboard" />
    </div>
  );
};

export default DashboardScreen;
