
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Ship, Anchor, Package, Users, DollarSign, Eye, Waves } from "lucide-react";
import { useState } from "react";
import BottomNavigation from "@/components/BottomNavigation";

const BOLCreationScreen = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Vessel & Voyage
    shippingLine: "",
    vesselName: "",
    imoNumber: "",
    voyageNumber: "",
    portOfLoading: "",
    portOfDischarge: "",
    etd: "",
    eta: "",
    // Cargo & Container
    containerNumber: "",
    containerType: "20GP",
    sealNumber: "",
    goodsDescription: "",
    grossWeight: "",
    measurement: "",
    // Parties
    shipper: "",
    consignee: "",
    notifyParty: "",
    incoterms: "FOB",
    // Freight
    freightPayable: "prepaid",
    oceanFreight: "",
    thc: "",
    documentation: ""
  });

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <Ship className="w-16 h-16 mx-auto text-deep-sea-blue mb-4" />
              <h2 className="text-xl font-bold text-deep-sea-blue">Vessel & Voyage Details</h2>
              <p className="text-gray-600">Select vessel and voyage information</p>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="shippingLine">Shipping Line</Label>
                  <Input
                    id="shippingLine"
                    placeholder="Maersk, MSC, CMA CGM..."
                    value={formData.shippingLine}
                    onChange={(e) => updateFormData("shippingLine", e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="vesselName">Vessel Name</Label>
                  <Input
                    id="vesselName"
                    placeholder="MSC OSCAR"
                    value={formData.vesselName}
                    onChange={(e) => updateFormData("vesselName", e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="imoNumber">IMO Number</Label>
                  <Input
                    id="imoNumber"
                    placeholder="1234567"
                    value={formData.imoNumber}
                    onChange={(e) => updateFormData("imoNumber", e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="voyageNumber">Voyage Number</Label>
                  <Input
                    id="voyageNumber"
                    placeholder="VY240115"
                    value={formData.voyageNumber}
                    onChange={(e) => updateFormData("voyageNumber", e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="portOfLoading">Port of Loading</Label>
                  <Input
                    id="portOfLoading"
                    placeholder="Cape Town, South Africa"
                    value={formData.portOfLoading}
                    onChange={(e) => updateFormData("portOfLoading", e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="portOfDischarge">Port of Discharge</Label>
                  <Input
                    id="portOfDischarge"
                    placeholder="Singapore"
                    value={formData.portOfDischarge}
                    onChange={(e) => updateFormData("portOfDischarge", e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="etd">ETD (Expected Departure)</Label>
                  <Input
                    id="etd"
                    type="date"
                    value={formData.etd}
                    onChange={(e) => updateFormData("etd", e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="eta">ETA (Expected Arrival)</Label>
                  <Input
                    id="eta"
                    type="date"
                    value={formData.eta}
                    onChange={(e) => updateFormData("eta", e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <Package className="w-16 h-16 mx-auto text-deep-sea-blue mb-4" />
              <h2 className="text-xl font-bold text-deep-sea-blue">Cargo & Container Management</h2>
              <p className="text-gray-600">Container and cargo details</p>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="containerNumber">Container Number</Label>
                  <Input
                    id="containerNumber"
                    placeholder="MSKU1234567"
                    value={formData.containerNumber}
                    onChange={(e) => updateFormData("containerNumber", e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="containerType">Container Type</Label>
                  <select
                    id="containerType"
                    className="w-full p-2 border border-gray-300 rounded-md"
                    value={formData.containerType}
                    onChange={(e) => updateFormData("containerType", e.target.value)}
                  >
                    <option value="20GP">20' General Purpose</option>
                    <option value="40GP">40' General Purpose</option>
                    <option value="40HC">40' High Cube</option>
                    <option value="20RF">20' Reefer</option>
                    <option value="40RF">40' Reefer</option>
                  </select>
                </div>
              </div>

              <div>
                <Label htmlFor="sealNumber">Seal Number</Label>
                <Input
                  id="sealNumber"
                  placeholder="SL123456"
                  value={formData.sealNumber}
                  onChange={(e) => updateFormData("sealNumber", e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="goodsDescription">Description of Goods</Label>
                <textarea
                  id="goodsDescription"
                  className="w-full p-2 border border-gray-300 rounded-md h-24"
                  placeholder="Electronic equipment, machinery parts, etc."
                  value={formData.goodsDescription}
                  onChange={(e) => updateFormData("goodsDescription", e.target.value)}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="grossWeight">Gross Weight (kg)</Label>
                  <Input
                    id="grossWeight"
                    type="number"
                    placeholder="18500"
                    value={formData.grossWeight}
                    onChange={(e) => updateFormData("grossWeight", e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="measurement">Measurement (CBM)</Label>
                  <Input
                    id="measurement"
                    type="number"
                    placeholder="33.2"
                    value={formData.measurement}
                    onChange={(e) => updateFormData("measurement", e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <Users className="w-16 h-16 mx-auto text-deep-sea-blue mb-4" />
              <h2 className="text-xl font-bold text-deep-sea-blue">Parties & Responsibilities</h2>
              <p className="text-gray-600">Define shipper, consignee, and terms</p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="shipper">Shipper</Label>
                <textarea
                  id="shipper"
                  className="w-full p-2 border border-gray-300 rounded-md h-20"
                  placeholder="Company Name&#10;Address&#10;Contact Details"
                  value={formData.shipper}
                  onChange={(e) => updateFormData("shipper", e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="consignee">Consignee</Label>
                <textarea
                  id="consignee"
                  className="w-full p-2 border border-gray-300 rounded-md h-20"
                  placeholder="Company Name&#10;Address&#10;Contact Details"
                  value={formData.consignee}
                  onChange={(e) => updateFormData("consignee", e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="notifyParty">Notify Party</Label>
                <textarea
                  id="notifyParty"
                  className="w-full p-2 border border-gray-300 rounded-md h-20"
                  placeholder="Same as consignee or different party"
                  value={formData.notifyParty}
                  onChange={(e) => updateFormData("notifyParty", e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="incoterms">INCOTERMS</Label>
                <select
                  id="incoterms"
                  className="w-full p-2 border border-gray-300 rounded-md"
                  value={formData.incoterms}
                  onChange={(e) => updateFormData("incoterms", e.target.value)}
                >
                  <option value="FOB">FOB - Free on Board</option>
                  <option value="CIF">CIF - Cost, Insurance & Freight</option>
                  <option value="CFR">CFR - Cost & Freight</option>
                  <option value="EXW">EXW - Ex Works</option>
                </select>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <DollarSign className="w-16 h-16 mx-auto text-deep-sea-blue mb-4" />
              <h2 className="text-xl font-bold text-deep-sea-blue">Freight & Charges</h2>
              <p className="text-gray-600">Calculate shipping costs and charges</p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="freightPayable">Freight Payable At</Label>
                <select
                  id="freightPayable"
                  className="w-full p-2 border border-gray-300 rounded-md"
                  value={formData.freightPayable}
                  onChange={(e) => updateFormData("freightPayable", e.target.value)}
                >
                  <option value="prepaid">Prepaid</option>
                  <option value="collect">Collect</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="oceanFreight">Ocean Freight (USD)</Label>
                  <Input
                    id="oceanFreight"
                    type="number"
                    placeholder="2500"
                    value={formData.oceanFreight}
                    onChange={(e) => updateFormData("oceanFreight", e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="thc">THC (USD)</Label>
                  <Input
                    id="thc"
                    type="number"
                    placeholder="350"
                    value={formData.thc}
                    onChange={(e) => updateFormData("thc", e.target.value)}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="documentation">Documentation Fee (USD)</Label>
                <Input
                  id="documentation"
                  type="number"
                  placeholder="75"
                  value={formData.documentation}
                  onChange={(e) => updateFormData("documentation", e.target.value)}
                />
              </div>

              <Card className="border-deep-sea-blue/20 bg-deep-sea-blue/5">
                <CardContent className="p-4">
                  <h3 className="font-semibold text-deep-sea-blue mb-2">Total Charges</h3>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Ocean Freight:</span>
                      <span>USD {formData.oceanFreight || '0'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>THC:</span>
                      <span>USD {formData.thc || '0'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Documentation:</span>
                      <span>USD {formData.documentation || '0'}</span>
                    </div>
                    <div className="border-t pt-1 flex justify-between font-semibold">
                      <span>Total:</span>
                      <span>USD {(Number(formData.oceanFreight || 0) + Number(formData.thc || 0) + Number(formData.documentation || 0)).toFixed(2)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <Eye className="w-16 h-16 mx-auto text-deep-sea-blue mb-4" />
              <h2 className="text-xl font-bold text-deep-sea-blue">BOL Preview & Generation</h2>
              <p className="text-gray-600">Review and generate your Bill of Lading</p>
            </div>

            <Card className="border-deep-sea-blue/20">
              <CardHeader>
                <CardTitle className="text-deep-sea-blue text-lg">Bill of Lading Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-semibold">Vessel:</span> {formData.vesselName}
                  </div>
                  <div>
                    <span className="font-semibold">Voyage:</span> {formData.voyageNumber}
                  </div>
                  <div>
                    <span className="font-semibold">Container:</span> {formData.containerNumber}
                  </div>
                  <div>
                    <span className="font-semibold">Weight:</span> {formData.grossWeight} kg
                  </div>
                </div>
                
                <div className="text-sm">
                  <span className="font-semibold">Route:</span> {formData.portOfLoading} → {formData.portOfDischarge}
                </div>
                
                <div className="text-sm">
                  <span className="font-semibold">Goods:</span> {formData.goodsDescription}
                </div>
              </CardContent>
            </Card>

            <div className="space-y-3">
              <Button 
                onClick={() => navigate('/bol/tracker')}
                className="w-full bg-deep-sea-blue hover:bg-blue-700"
              >
                Generate BOL & Track Voyage
              </Button>
              <Button 
                variant="outline" 
                className="w-full border-deep-sea-blue text-deep-sea-blue"
              >
                Save as Draft
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-gradient-to-r from-deep-sea-blue to-blue-800 text-white p-6 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Waves className="w-full h-full" />
        </div>
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => currentStep > 1 ? setCurrentStep(currentStep - 1) : navigate('/bol')}
              className="text-white hover:bg-white/10"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold flex items-center gap-2">
                <Anchor className="w-6 h-6" />
                Create Bill of Lading
              </h1>
              <p className="text-blue-200">Step {currentStep} of 5</p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-white/20 rounded-full h-2">
            <div 
              className="bg-white h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / 5) * 100}%` }}
            />
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="p-6">
        {renderStep()}

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={() => currentStep > 1 ? setCurrentStep(currentStep - 1) : navigate('/bol')}
            className="border-deep-sea-blue text-deep-sea-blue"
          >
            {currentStep === 1 ? 'Cancel' : 'Previous'}
          </Button>
          <Button
            onClick={() => currentStep < 5 ? setCurrentStep(currentStep + 1) : navigate('/bol/tracker')}
            className="bg-deep-sea-blue hover:bg-blue-700"
          >
            {currentStep === 5 ? 'Generate BOL' : 'Next'}
          </Button>
        </div>
      </div>

      <BottomNavigation currentPage="bol" />
    </div>
  );
};

export default BOLCreationScreen;
