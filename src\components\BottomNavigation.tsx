
import { useNavigate } from "react-router-dom";
import { Home, Scan, FileText, Package, Plane, Anchor, Truck } from "lucide-react";

interface BottomNavigationProps {
  currentPage: string;
}

const BottomNavigation = ({ currentPage }: BottomNavigationProps) => {
  const navigate = useNavigate();

  const navItems = [
    { id: "dashboard", icon: Home, label: "Home", path: "/dashboard" },
    { id: "capture", icon: Scan, label: "Scan", path: "/capture" },
    { id: "boe", icon: FileText, label: "BOE", path: "/boe" },
    { id: "rfm", icon: Truck, label: "RFM", path: "/rfm" },
    { id: "awb", icon: Plane, label: "AWB", path: "/awb" },
    { id: "bol", icon: Anchor, label: "BOL", path: "/bol" },
    { id: "manifest", icon: Package, label: "Manifest", path: "/manifest" },
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-1 py-2">
      <div className="flex justify-around">
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = currentPage === item.id;
          
          const getActiveColor = () => {
            if (item.id === "capture") return "text-sars-green";
            if (item.id === "awb") return "text-sky-500";
            if (item.id === "manifest") return "text-teal-600";
            if (item.id === "bol") return "text-deep-sea-blue";
            if (item.id === "rfm") return "text-terracotta";
            return "text-sars-blue";
          };
          
          return (
            <button
              key={item.id}
              onClick={() => navigate(item.path)}
              className={`flex flex-col items-center py-1 px-1 transition-colors ${
                isActive 
                  ? getActiveColor()
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Icon className={`w-4 h-4 ${isActive ? getActiveColor() : ''}`} />
              <span className="text-xs mt-1">{item.label}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default BottomNavigation;
