import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { Scan, FileText, Send, ChevronLeft, ChevronRight } from "lucide-react";
const OnboardingScreen = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const navigate = useNavigate();
  const slides = [{
    icon: <Scan className="w-16 h-16 text-sars-green" />,
    title: "Scan Invoices",
    description: "Capture commercial invoices with your camera for instant data extraction"
  }, {
    icon: <FileText className="w-16 h-16 text-sars-green" />,
    title: "Auto-generate BOE",
    description: "Automatically generate SARS Bills of Entry with verified customs data"
  }, {
    icon: <Send className="w-16 h-16 text-sars-green" />,
    title: "Submit to SARS",
    description: "Submit directly to SARS with real-time tracking and status updates"
  }];
  const nextSlide = () => {
    if (currentSlide < slides.length - 1) {
      setCurrentSlide(currentSlide + 1);
    }
  };
  const prevSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
    }
  };
  return <div className="min-h-screen bg-gradient-to-br from-sars-blue to-blue-900 flex flex-col items-center justify-center p-6 text-white">
      {/* SARS Logo and Title */}
      <div className="text-center mb-12">
        <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mb-4 mx-auto">
          <div className="text-sars-blue font-bold text-2xl">SARS</div>
        </div>
        <h1 className="text-3xl font-bold mb-2">Freight Assistant</h1>
        <p className="text-blue-200">Professional Documents Generation</p>
      </div>

      {/* Slides Container */}
      <Card className="w-full max-w-md bg-white/10 backdrop-blur-md border-white/20 p-8 text-center min-h-[300px] flex flex-col justify-center">
        <div className="animate-fade-in">
          <div className="flex justify-center mb-6">
            {slides[currentSlide].icon}
          </div>
          <h2 className="text-2xl font-semibold mb-4">{slides[currentSlide].title}</h2>
          <p className="text-blue-100 leading-relaxed">{slides[currentSlide].description}</p>
        </div>
      </Card>

      {/* Navigation */}
      <div className="flex items-center justify-between w-full max-w-md mt-8">
        <Button variant="ghost" size="sm" onClick={prevSlide} disabled={currentSlide === 0} className="text-white hover:bg-white/10">
          <ChevronLeft className="w-4 h-4 mr-1" />
          Back
        </Button>

        {/* Slide Indicators */}
        <div className="flex space-x-2">
          {slides.map((_, index) => <div key={index} className={`w-2 h-2 rounded-full transition-colors ${index === currentSlide ? 'bg-sars-green' : 'bg-white/30'}`} />)}
        </div>

        <Button variant="ghost" size="sm" onClick={nextSlide} disabled={currentSlide === slides.length - 1} className="text-white hover:bg-white/10">
          Next
          <ChevronRight className="w-4 h-4 ml-1" />
        </Button>
      </div>

      {/* Get Started Button */}
      <Button onClick={() => navigate('/dashboard')} className="mt-12 bg-sars-green hover:bg-green-600 text-white px-8 py-3 text-lg font-medium animate-pulse-glow" size="lg">
        Get Started
      </Button>

      {/* Skip Option */}
      <button onClick={() => navigate('/dashboard')} className="mt-4 text-blue-200 hover:text-white transition-colors underline">
        Skip Introduction
      </button>
    </div>;
};
export default OnboardingScreen;