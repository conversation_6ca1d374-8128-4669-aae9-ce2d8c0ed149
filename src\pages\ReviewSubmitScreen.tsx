
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, FileText, Send, Download, CheckCircle, AlertTriangle } from "lucide-react";
import BottomNavigation from "@/components/BottomNavigation";

const ReviewSubmitScreen = () => {
  const navigate = useNavigate();
  const [checklist, setChecklist] = useState({
    vatVerified: true,
    invoiceAttached: false,
    digitalSignature: false
  });

  const updateChecklist = (key: string, value: boolean) => {
    setChecklist(prev => ({ ...prev, [key]: value }));
  };

  const isReadyToSubmit = Object.values(checklist).every(Boolean);

  const handleSubmit = () => {
    if (isReadyToSubmit) {
      navigate('/tracker');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-sars-blue text-white p-4">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/generator')}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-lg font-semibold">Review & Submit</h1>
            <p className="text-blue-200 text-sm">BOE-2024-004</p>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* PDF Preview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              BOE Document Preview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-white border-2 border-dashed border-gray-300 rounded-lg h-48 flex items-center justify-center cursor-pointer hover:border-sars-blue transition-colors">
              <div className="text-center">
                <FileText className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                <p className="text-sm font-medium">BOE-2024-004.pdf</p>
                <p className="text-xs text-gray-500">Click to view full document</p>
              </div>
            </div>
            <div className="mt-4 flex gap-2">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-1" />
                Download
              </Button>
              <Button variant="outline" size="sm">
                View Full PDF
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Submission Checklist */}
        <Card>
          <CardHeader>
            <CardTitle>Submission Checklist</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <Checkbox
                id="vat-verified"
                checked={checklist.vatVerified}
                onCheckedChange={(checked) => updateChecklist('vatVerified', checked as boolean)}
              />
              <label htmlFor="vat-verified" className="text-sm flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-sars-green" />
                Supplier VAT verified
              </label>
            </div>
            
            <div className="flex items-center space-x-3">
              <Checkbox
                id="invoice-attached"
                checked={checklist.invoiceAttached}
                onCheckedChange={(checked) => updateChecklist('invoiceAttached', checked as boolean)}
              />
              <label htmlFor="invoice-attached" className="text-sm flex items-center gap-2">
                {checklist.invoiceAttached ? (
                  <CheckCircle className="w-4 h-4 text-sars-green" />
                ) : (
                  <AlertTriangle className="w-4 h-4 text-yellow-500" />
                )}
                Attach commercial invoice
              </label>
            </div>
            
            <div className="flex items-center space-x-3">
              <Checkbox
                id="digital-signature"
                checked={checklist.digitalSignature}
                onCheckedChange={(checked) => updateChecklist('digitalSignature', checked as boolean)}
              />
              <label htmlFor="digital-signature" className="text-sm flex items-center gap-2">
                {checklist.digitalSignature ? (
                  <CheckCircle className="w-4 h-4 text-sars-green" />
                ) : (
                  <AlertTriangle className="w-4 h-4 text-yellow-500" />
                )}
                Digital signature
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Submission Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Total Customs Value:</span>
                <span className="font-medium">R 23,000</span>
              </div>
              <div className="flex justify-between">
                <span>Total Duty & Taxes:</span>
                <span className="font-medium">R 6,095</span>
              </div>
              <div className="flex justify-between">
                <span>Processing Time:</span>
                <span className="font-medium">24-48 hours</span>
              </div>
              <hr className="my-2" />
              <div className="flex justify-between font-semibold text-sars-blue">
                <span>Total Amount Due:</span>
                <span>R 29,095</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button
            onClick={handleSubmit}
            disabled={!isReadyToSubmit}
            className={`w-full ${
              isReadyToSubmit 
                ? 'bg-sars-green hover:bg-green-600' 
                : 'bg-gray-400 cursor-not-allowed'
            }`}
          >
            <Send className="w-4 h-4 mr-2" />
            Submit to SARS
          </Button>
          
          <div className="grid grid-cols-2 gap-3">
            <Button variant="outline">
              Save Draft
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {!isReadyToSubmit && (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-yellow-700">
                <AlertTriangle className="w-4 h-4" />
                <span className="text-sm">Please complete all checklist items before submitting</span>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <BottomNavigation currentPage="capture" />
    </div>
  );
};

export default ReviewSubmitScreen;
