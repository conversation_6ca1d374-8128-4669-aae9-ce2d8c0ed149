import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Plus, Package, Thermometer, Scale, Box } from "lucide-react";

const ContainerManagementScreen = () => {
  const navigate = useNavigate();
  const [selectedContainer, setSelectedContainer] = useState<string | null>("MSKU48271");

  const containers = [
    {
      id: "MSKU48271",
      type: "40ft Standard",
      status: "In Use",
      utilization: 75,
      weight: "18,500 kg",
      maxWeight: "26,760 kg",
      boeCount: 8,
      temperature: null,
    },
    {
      id: "TCLU89043",
      type: "20ft Reefer",
      status: "Available",
      utilization: 0,
      weight: "0 kg",
      maxWeight: "21,600 kg",
      boeCount: 0,
      temperature: "-18°C",
    },
  ];

  const selectedContainerData = containers.find(c => c.id === selectedContainer);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-teal-600 text-white p-6">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/manifest/create')}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold">Container Management</h1>
            <p className="text-teal-200">Step 2 of 5 - Containers</p>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-white/20 rounded-full h-2">
          <div className="bg-white h-2 rounded-full w-2/5"></div>
        </div>
      </div>

      <div className="p-6">
        {/* Container Matrix */}
        <Card className="mb-6">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-teal-800">Container Matrix</CardTitle>
            <Button size="sm" className="bg-teal-600 hover:bg-teal-700">
              <Plus className="w-4 h-4 mr-2" />
              Add Container
            </Button>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4">
              {containers.map((container) => (
                <button
                  key={container.id}
                  onClick={() => setSelectedContainer(container.id)}
                  className={`p-4 rounded-lg border-2 text-left transition-all ${
                    selectedContainer === container.id
                      ? 'border-teal-600 bg-teal-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Package className="w-5 h-5 text-teal-600" />
                      <span className="font-bold">{container.id}</span>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      container.status === 'In Use' 
                        ? 'bg-yellow-100 text-yellow-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {container.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{container.type}</p>
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span>{container.boeCount} BOEs</span>
                    <span>{container.utilization}% Full</span>
                    {container.temperature && (
                      <span className="flex items-center gap-1">
                        <Thermometer className="w-3 h-3" />
                        {container.temperature}
                      </span>
                    )}
                  </div>
                  {/* Utilization Bar */}
                  <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-teal-600 h-2 rounded-full transition-all"
                      style={{ width: `${container.utilization}%` }}
                    ></div>
                  </div>
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Container Detail Panel */}
        {selectedContainerData && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-teal-800">Container Details - {selectedContainerData.id}</CardTitle>
            </CardHeader>
            <CardContent>
              {/* 3D Container Visualization Placeholder */}
              <div className="bg-gray-100 rounded-lg p-8 mb-6 text-center">
                <Box className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600">3D Container Visualization</p>
                <p className="text-sm text-gray-500">Interactive container space grid</p>
              </div>

              {/* Metrics Grid */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <Card className="bg-blue-50">
                  <CardContent className="p-4 text-center">
                    <Scale className="w-6 h-6 mx-auto text-blue-600 mb-2" />
                    <div className="text-2xl font-bold text-blue-800">{selectedContainerData.weight}</div>
                    <div className="text-xs text-blue-600">Current Weight</div>
                    <div className="text-xs text-gray-500">Max: {selectedContainerData.maxWeight}</div>
                  </CardContent>
                </Card>

                <Card className="bg-teal-50">
                  <CardContent className="p-4 text-center">
                    <Package className="w-6 h-6 mx-auto text-teal-600 mb-2" />
                    <div className="text-2xl font-bold text-teal-800">{selectedContainerData.utilization}%</div>
                    <div className="text-xs text-teal-600">Space Utilized</div>
                    <div className="text-xs text-gray-500">{selectedContainerData.boeCount} BOEs loaded</div>
                  </CardContent>
                </Card>
              </div>

              {/* Temperature Log for Reefers */}
              {selectedContainerData.temperature && (
                <Card className="bg-blue-50 mb-4">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Thermometer className="w-5 h-5 text-blue-600" />
                      <span className="font-medium">Temperature Control</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold text-blue-800">{selectedContainerData.temperature}</span>
                      <span className="text-sm text-blue-600">Target: -18°C</span>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Actions */}
              <div className="flex gap-2">
                <Button 
                  className="flex-1 bg-teal-600 hover:bg-teal-700"
                  onClick={() => navigate('/manifest/assignment')}
                >
                  Add BOEs
                </Button>
                <Button variant="outline" className="border-teal-600 text-teal-600">
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation */}
        <div className="flex gap-4">
          <Button variant="outline" className="flex-1" onClick={() => navigate('/manifest/create')}>
            Back
          </Button>
          <Button 
            className="flex-1 bg-teal-600 hover:bg-teal-700"
            onClick={() => navigate('/manifest/assignment')}
          >
            Assign BOEs
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ContainerManagementScreen;
