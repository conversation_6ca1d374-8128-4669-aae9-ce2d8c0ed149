
import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { Camera, Image, Flashlight, FlashlightOff, ArrowLeft, Layers, Home, User, Bell } from "lucide-react";
import BottomNavigation from "@/components/BottomNavigation";

const InvoiceCaptureScreen = () => {
  const [flashEnabled, setFlashEnabled] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleCapture = async () => {
    setIsCapturing(true);
    
    // Check if device supports camera
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
          video: { 
            facingMode: 'environment', // Use back camera
            width: { ideal: 1920 },
            height: { ideal: 1080 }
          } 
        });
        
        // Create video element to show camera feed
        const video = document.createElement('video');
        video.srcObject = stream;
        video.play();
        
        // For demo purposes, simulate capture after 2 seconds
        setTimeout(() => {
          stream.getTracks().forEach(track => track.stop());
          setIsCapturing(false);
          navigate('/document-selection');
        }, 2000);
        
      } catch (error) {
        console.error('Camera access failed:', error);
        // Fallback to file input
        fileInputRef.current?.click();
        setIsCapturing(false);
      }
    } else {
      // Fallback to file input for devices without camera support
      fileInputRef.current?.click();
      setIsCapturing(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      navigate('/document-selection');
    }
  };

  return (
    <div className="min-h-screen bg-black flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 text-white bg-black/50 backdrop-blur-sm z-10">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate('/dashboard')}
          className="text-white hover:bg-white/10"
        >
          <ArrowLeft className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-medium">Capture Document</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setFlashEnabled(!flashEnabled)}
            className="text-white hover:bg-white/10"
          >
            {flashEnabled ? <Flashlight className="w-5 h-5" /> : <FlashlightOff className="w-5 h-5" />}
          </Button>
          <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
            <Bell className="w-5 h-5" />
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            className="text-white hover:bg-white/10"
            onClick={() => navigate('/dashboard')}
          >
            <Home className="w-5 h-5" />
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            className="text-white hover:bg-white/10"
            onClick={() => navigate('/profile')}
          >
            <User className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Camera Viewfinder */}
      <div className="flex-1 relative bg-gray-900 flex items-center justify-center">
        {/* Corner Guides */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-20 left-8 w-8 h-8 border-l-4 border-t-4 border-sars-green"></div>
          <div className="absolute top-20 right-8 w-8 h-8 border-r-4 border-t-4 border-sars-green"></div>
          <div className="absolute bottom-32 left-8 w-8 h-8 border-l-4 border-b-4 border-sars-green"></div>
          <div className="absolute bottom-32 right-8 w-8 h-8 border-r-4 border-b-4 border-sars-green"></div>
        </div>

        {/* Mock Camera View */}
        <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
          <div className="text-center text-white/70">
            <Camera className="w-16 h-16 mx-auto mb-4" />
            <p>Position document within guides</p>
            {isCapturing && <p className="text-sars-green animate-pulse mt-2">Capturing...</p>}
          </div>
        </div>

        {/* Detection Overlay */}
        {!isCapturing && (
          <Card className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-sars-green/90 backdrop-blur-sm border-sars-green text-white p-4">
            <div className="text-center animate-pulse">
              <p className="text-sm font-medium">📄 Document detected</p>
              <p className="text-xs">Tap to capture</p>
            </div>
          </Card>
        )}
      </div>

      {/* Bottom Controls */}
      <div className="p-6 bg-black/80 backdrop-blur-sm">
        <div className="flex items-center justify-between mb-6">
          {/* Gallery */}
          <Button
            variant="outline"
            size="sm"
            className="border-white text-white hover:bg-white/10"
            onClick={() => fileInputRef.current?.click()}
          >
            <Image className="w-5 h-5 mr-2" />
            Gallery
          </Button>

          {/* Capture Button */}
          <Button
            onClick={handleCapture}
            disabled={isCapturing}
            className="w-20 h-20 rounded-full bg-sars-green hover:bg-green-600 animate-pulse-glow disabled:opacity-50"
          >
            <Camera className="w-8 h-8" />
          </Button>

          {/* Multi-page */}
          <Button
            variant="outline"
            size="sm"
            className="border-white text-white hover:bg-white/10"
          >
            <Layers className="w-5 h-5 mr-2" />
            Multi
          </Button>
        </div>

        {/* Instructions */}
        <div className="text-center text-white/80">
          <p className="text-sm">Ensure document is clearly visible and well-lit</p>
          <p className="text-xs text-white/60 mt-1">Supported formats: PDF, JPG, PNG</p>
        </div>
      </div>

      {/* Hidden file input for fallback */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*,application/pdf"
        onChange={handleFileSelect}
        className="hidden"
        capture="environment"
      />

      <BottomNavigation currentPage="capture" />
    </div>
  );
};

export default InvoiceCaptureScreen;
