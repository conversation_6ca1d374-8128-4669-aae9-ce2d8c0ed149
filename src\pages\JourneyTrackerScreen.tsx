
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { 
  ArrowLeft, 
  Truck, 
  MapPin, 
  Clock, 
  Fuel,
  Shield,
  AlertTriangle,
  CheckCircle,
  Phone,
  Navigation,
  Camera
} from "lucide-react";

const JourneyTrackerScreen = () => {
  const navigate = useNavigate();

  const checkpoints = [
    { id: 1, location: "Cape Town (Origin)", time: "08:00", status: "completed", type: "departure" },
    { id: 2, location: "Beaufort West", time: "12:30", status: "completed", type: "checkpoint" },
    { id: 3, location: "Kimberley", time: "18:45", status: "completed", type: "rest" },
    { id: 4, location: "Johannesburg", time: "06:20", status: "current", type: "checkpoint" },
    { id: 5, location: "Beitbridge Border", time: "14:30", status: "pending", type: "border" },
    { id: 6, location: "Harare (Destination)", time: "20:00", status: "pending", type: "arrival" },
  ];

  const getStatusIcon = (status: string, type: string) => {
    if (status === "completed") return <CheckCircle className="w-4 h-4 text-sars-green" />;
    if (status === "current") return <Truck className="w-4 h-4 text-terracotta animate-pulse" />;
    if (type === "border") return <Shield className="w-4 h-4 text-yellow-500" />;
    return <MapPin className="w-4 h-4 text-gray-400" />;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "text-sars-green";
      case "current": return "text-terracotta";
      default: return "text-gray-500";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-terracotta text-white p-6">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/rfm')}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div className="flex-1">
            <h1 className="text-xl font-bold">Journey Tracker</h1>
            <p className="text-orange-200">RFM-2024-016 • Live tracking</p>
          </div>
          <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
            <Phone className="w-5 h-5" />
          </Button>
        </div>

        {/* Current Status */}
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Truck className="w-5 h-5" />
                  <span className="font-medium">GP 123-456</span>
                </div>
                <p className="text-sm text-orange-200">72km to Beitbridge Border</p>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold">14:30</div>
                <div className="text-xs text-orange-200">ETA Border</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Live Dashboard */}
      <div className="p-6 space-y-6">
        {/* Vehicle Telemetry */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-terracotta">
              <Truck className="w-5 h-5" />
              Vehicle Telemetry
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-terracotta">82</div>
                <div className="text-xs text-gray-600">km/h</div>
                <div className="text-xs text-sars-green">Within limits</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">68%</div>
                <div className="text-xs text-gray-600">Fuel level</div>
                <div className="text-xs text-gray-500">~450km range</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">32.1t</div>
                <div className="text-xs text-gray-600">Load weight</div>
                <div className="text-xs text-sars-green">Balanced</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Route Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-terracotta">
              <Navigation className="w-5 h-5" />
              Route Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {checkpoints.map((checkpoint) => (
                <div key={checkpoint.id} className="flex items-center gap-4">
                  <div className="flex-shrink-0">
                    {getStatusIcon(checkpoint.status, checkpoint.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className={`font-medium ${getStatusColor(checkpoint.status)}`}>
                        {checkpoint.location}
                      </span>
                      <span className="text-sm text-gray-500">{checkpoint.time}</span>
                    </div>
                    {checkpoint.status === "current" && (
                      <div className="text-xs text-terracotta mt-1">
                        📍 Current location • Driver check-in required
                      </div>
                    )}
                    {checkpoint.type === "border" && checkpoint.status === "pending" && (
                      <div className="text-xs text-yellow-600 mt-1">
                        ⚠️ Expected wait time: 4.5 hours
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Border Information */}
        <Card className="border-yellow-500/20 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-700">
              <Shield className="w-5 h-5" />
              Beitbridge Border Post
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>Queue Status:</span>
              <span className="text-yellow-600 font-medium">High Traffic</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Estimated Wait:</span>
              <span className="font-medium">4.5 hours</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Border Agent:</span>
              <span className="font-medium">Contact Available</span>
            </div>
            
            <div className="bg-white p-3 rounded border">
              <h4 className="font-medium text-sm mb-2">📋 Document Checklist</h4>
              <div className="space-y-1 text-xs">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-3 h-3 text-sars-green" />
                  <span>SARS RFM (DA185)</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-3 h-3 text-sars-green" />
                  <span>Driver's Passport</span>
                </div>
                <div className="flex items-center gap-2">
                  <AlertTriangle className="w-3 h-3 text-yellow-500" />
                  <span>Transit Bond (Purchase required)</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Driver Actions */}
        <div className="grid grid-cols-2 gap-4">
          <Button className="h-16 flex flex-col bg-terracotta hover:bg-orange-700">
            <Camera className="w-5 h-5 mb-1" />
            <span className="text-xs">Photo Evidence</span>
          </Button>
          <Button variant="outline" className="h-16 flex flex-col border-terracotta text-terracotta">
            <Phone className="w-5 h-5 mb-1" />
            <span className="text-xs">Emergency SOS</span>
          </Button>
        </div>

        {/* Driver Compliance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-terracotta">
              <Clock className="w-5 h-5" />
              Driver Compliance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>Driving Time Today:</span>
              <span className="font-medium">8h 30m</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Next Rest Required:</span>
              <span className="text-terracotta font-medium">3h 30m</span>
            </div>
            <div className="bg-sars-green/10 p-3 rounded">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-sars-green" />
                <span className="text-sm text-sars-green">Compliance: On track</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default JourneyTrackerScreen;
