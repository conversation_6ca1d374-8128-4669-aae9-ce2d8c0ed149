
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { Anchor, Plus, Waves, Ship, Clock, CheckCircle, AlertTriangle, Package } from "lucide-react";
import BottomNavigation from "@/components/BottomNavigation";

const BOLDashboard = () => {
  const navigate = useNavigate();

  const recentBOLs = [
    { id: "BOL-2024-001", vessel: "MSC OSCAR", voyage: "VY240115", consignee: "Maritime Imports Ltd", status: "In Transit", etd: "2024-01-15" },
    { id: "BOL-2024-002", vessel: "MAERSK ESSEX", voyage: "VY240112", consignee: "Ocean Cargo SA", status: "Port Hold", etd: "2024-01-14" },
    { id: "BOL-2024-003", vessel: "CMA CGM TITAN", voyage: "VY240110", consignee: "Shipping Express", status: "Delivered", etd: "2024-01-13" },
    { id: "BOL-2024-004", vessel: "HAPAG LLOYD", voyage: "VY240108", consignee: "Freight Masters", status: "Draft", etd: "2024-01-12" },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Delivered": return <CheckCircle className="w-4 h-4 text-sars-green" />;
      case "In Transit": return <Ship className="w-4 h-4 text-deep-sea-blue" />;
      case "Port Hold": return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Delivered": return "text-sars-green";
      case "In Transit": return "text-deep-sea-blue";
      case "Port Hold": return "text-yellow-600";
      default: return "text-gray-600";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header with wave pattern */}
      <div className="bg-gradient-to-r from-deep-sea-blue to-blue-800 text-white p-6 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Waves className="w-full h-full" />
        </div>
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold flex items-center gap-2">
                <Anchor className="w-8 h-8" />
                Bill of Lading Center
              </h1>
              <p className="text-blue-200">Manage ocean freight documentation</p>
            </div>
          </div>

          {/* Stats Card */}
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardContent className="p-4">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold">24</div>
                  <div className="text-xs text-blue-200">Active BOLs</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">3.2</div>
                  <div className="text-xs text-blue-200">Avg Port Days</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">88%</div>
                  <div className="text-xs text-blue-200">On-time Rate</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-6">
        <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-3 gap-4 mb-8">
          <Button
            onClick={() => navigate('/bol/create')}
            className="h-20 flex flex-col bg-deep-sea-blue hover:bg-blue-700"
          >
            <Plus className="w-6 h-6 mb-1" />
            <span className="text-xs">Create BOL</span>
          </Button>
          <Button
            onClick={() => navigate('/manifest')}
            variant="outline"
            className="h-20 flex flex-col border-deep-sea-blue text-deep-sea-blue hover:bg-deep-sea-blue hover:text-white"
          >
            <Package className="w-6 h-6 mb-1" />
            <span className="text-xs">Import Manifest</span>
          </Button>
          <Button
            onClick={() => navigate('/bol/tracker')}
            variant="outline"
            className="h-20 flex flex-col border-deep-sea-blue text-deep-sea-blue hover:bg-deep-sea-blue hover:text-white"
          >
            <Ship className="w-6 h-6 mb-1" />
            <span className="text-xs">Voyage Tracker</span>
          </Button>
        </div>

        {/* Status Tiles */}
        <div className="grid grid-cols-2 gap-4 mb-8">
          <Card className="border-deep-sea-blue/20 bg-deep-sea-blue/5">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-deep-sea-blue">8</div>
              <div className="text-sm text-gray-600">In Transit</div>
            </CardContent>
          </Card>
          <Card className="border-yellow-300/50 bg-yellow-50">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-yellow-600">3</div>
              <div className="text-sm text-gray-600">Port Hold</div>
            </CardContent>
          </Card>
        </div>

        {/* Recent BOLs */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Recent Bills of Lading</h2>
          <div className="space-y-3">
            {recentBOLs.map((bol) => (
              <Card key={bol.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-deep-sea-blue">{bol.id}</span>
                        <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-deep-sea-blue">
                          BOL
                        </span>
                        {getStatusIcon(bol.status)}
                      </div>
                      <p className="text-sm text-gray-600">
                        {bol.vessel} - {bol.voyage}
                      </p>
                      <p className="text-sm text-gray-600">{bol.consignee}</p>
                      <p className="text-xs text-gray-500">ETD: {bol.etd}</p>
                    </div>
                    <div className={`text-sm font-medium ${getStatusColor(bol.status)}`}>
                      {bol.status}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Shipping Insights */}
        <Card className="border-deep-sea-blue/20 bg-deep-sea-blue/5">
          <CardHeader className="pb-3">
            <CardTitle className="text-deep-sea-blue text-sm flex items-center gap-2">
              <Waves className="w-4 h-4" />
              Maritime Updates
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-sm text-gray-700">
              Port congestion at Durban expected to clear by January 20th. Consider alternative routing via Cape Town for urgent shipments.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Floating Action Button */}
      <Button
        onClick={() => navigate('/bol/create')}
        className="fixed bottom-24 right-6 w-14 h-14 rounded-full bg-deep-sea-blue hover:bg-blue-700 shadow-lg animate-pulse-glow"
        size="sm"
      >
        <Plus className="w-6 h-6" />
      </Button>

      <BottomNavigation currentPage="bol" />
    </div>
  );
};

export default BOLDashboard;
