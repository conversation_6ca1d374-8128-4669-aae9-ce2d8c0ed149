
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useNavigate } from "react-router-dom";
import { 
  ArrowLeft, 
  Truck, 
  User, 
  Route, 
  Package,
  MapPin,
  Clock,
  Shield,
  FileText,
  CheckCircle,
  AlertTriangle,
  Navigation
} from "lucide-react";
import { Progress } from "@/components/ui/progress";

const RFMCreationScreen = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    vehicleReg: "",
    driverName: "",
    driverLicense: "",
    route: "",
    cargoWeight: "",
    borderDocs: false
  });

  const steps = [
    { id: 1, title: "Vehicle & Driver", icon: Truck },
    { id: 2, title: "Route Planning", icon: Route },
    { id: 3, title: "Cargo Loading", icon: Package },
    { id: 4, title: "Border Compliance", icon: Shield },
    { id: 5, title: "RFM Generation", icon: FileText }
  ];

  const progress = (currentStep / steps.length) * 100;

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    } else {
      navigate('/rfm/tracker');
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-terracotta">
                  <Truck className="w-5 h-5" />
                  Vehicle Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="vehicleReg">Vehicle Registration</Label>
                  <Input 
                    id="vehicleReg" 
                    placeholder="e.g., GP 123-456"
                    value={formData.vehicleReg}
                    onChange={(e) => setFormData({...formData, vehicleReg: e.target.value})}
                  />
                  <p className="text-xs text-gray-500 mt-1">🔍 Scan license plate or enter manually</p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Load Capacity</Label>
                    <div className="bg-gray-100 p-3 rounded text-sm">
                      <div className="flex justify-between">
                        <span>Max Tonnage:</span>
                        <span className="font-medium">34t</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Available:</span>
                        <span className="font-medium text-sars-green">28t</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <Label>Technical Status</Label>
                    <div className="bg-sars-green/10 p-3 rounded text-sm">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-sars-green" />
                        <span className="text-sars-green">Roadworthy Valid</span>
                      </div>
                      <p className="text-xs text-gray-600">Expires: 2024-08-15</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-terracotta">
                  <User className="w-5 h-5" />
                  Driver Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="driverName">Driver Name</Label>
                  <Input 
                    id="driverName" 
                    placeholder="Full name"
                    value={formData.driverName}
                    onChange={(e) => setFormData({...formData, driverName: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="driverLicense">PDP License Number</Label>
                  <Input 
                    id="driverLicense" 
                    placeholder="PDP-123456789"
                    value={formData.driverLicense}
                    onChange={(e) => setFormData({...formData, driverLicense: e.target.value})}
                  />
                </div>
                <div className="bg-terracotta/10 p-3 rounded">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-4 h-4 text-terracotta" />
                    <span className="text-sm font-medium text-terracotta">Rest Hour Compliance</span>
                  </div>
                  <div className="text-xs text-gray-600">
                    Last rest: 8 hours ago | Next required: 6 hours
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-terracotta">
                  <Navigation className="w-5 h-5" />
                  Route Planning
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-gray-100 p-4 rounded">
                  <h4 className="font-medium mb-2">🗺️ Interactive SADC Route Map</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>📍 Cape Town (Origin)</span>
                      <span>0 km</span>
                    </div>
                    <div className="flex justify-between">
                      <span>📍 Johannesburg (Stop 1)</span>
                      <span>1,400 km</span>
                    </div>
                    <div className="flex justify-between">
                      <span>📍 Harare (Destination)</span>
                      <span>1,950 km</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Border Crossings</Label>
                    <div className="bg-yellow-50 p-3 rounded text-sm">
                      <div className="flex items-center gap-2 mb-1">
                        <MapPin className="w-4 h-4 text-yellow-600" />
                        <span className="font-medium">Beitbridge Border</span>
                      </div>
                      <p className="text-xs text-gray-600">Current wait: 4.5 hours</p>
                      <p className="text-xs text-yellow-600">⚠️ High traffic expected</p>
                    </div>
                  </div>
                  <div>
                    <Label>Toll Fees</Label>
                    <div className="bg-gray-100 p-3 rounded text-sm">
                      <div className="flex justify-between">
                        <span>SA Tolls:</span>
                        <span>R 485</span>
                      </div>
                      <div className="flex justify-between">
                        <span>ZIM Tolls:</span>
                        <span>USD 23</span>
                      </div>
                      <div className="flex justify-between font-medium border-t pt-1">
                        <span>Total Est:</span>
                        <span>R 835</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-terracotta">
                  <Package className="w-5 h-5" />
                  Cargo Loading & Distribution
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-terracotta/10 p-4 rounded">
                  <h4 className="font-medium mb-2">🚛 Load Bay Visualizer</h4>
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div className="bg-blue-200 p-2 rounded text-center">Front Axle<br/>8.2t</div>
                    <div className="bg-yellow-200 p-2 rounded text-center">Mid Axle<br/>12.4t</div>
                    <div className="bg-green-200 p-2 rounded text-center">Rear Axle<br/>11.8t</div>
                  </div>
                  <div className="mt-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Load:</span>
                      <span className="font-medium">32.4t</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Legal Limit:</span>
                      <span className="font-medium">34t</span>
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <CheckCircle className="w-4 h-4 text-sars-green" />
                      <span className="text-sars-green text-sm">Within legal limits</span>
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="cargoWeight">Total Cargo Weight (tons)</Label>
                  <Input 
                    id="cargoWeight" 
                    type="number"
                    placeholder="28.5"
                    value={formData.cargoWeight}
                    onChange={(e) => setFormData({...formData, cargoWeight: e.target.value})}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Card className="border-sars-green/20">
                    <CardContent className="p-3">
                      <div className="text-center">
                        <CheckCircle className="w-8 h-8 mx-auto text-sars-green mb-1" />
                        <p className="text-sm font-medium">Axle Balance</p>
                        <p className="text-xs text-gray-600">Optimal distribution</p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="border-yellow-500/20">
                    <CardContent className="p-3">
                      <div className="text-center">
                        <AlertTriangle className="w-8 h-8 mx-auto text-yellow-500 mb-1" />
                        <p className="text-sm font-medium">Weight Check</p>
                        <p className="text-xs text-gray-600">Verify at next weigh bridge</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-terracotta">
                  <Shield className="w-5 h-5" />
                  Border Compliance Assistant
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="border rounded p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">🇿🇦 South Africa → 🇿🇼 Zimbabwe</span>
                      <CheckCircle className="w-5 h-5 text-sars-green" />
                    </div>
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span>✅ SARS RFM (DA185)</span>
                        <span className="text-sars-green">Ready</span>
                      </div>
                      <div className="flex justify-between">
                        <span>✅ CBL Documents</span>
                        <span className="text-sars-green">Prepared</span>
                      </div>
                      <div className="flex justify-between">
                        <span>⚠️ Transit Bond</span>
                        <span className="text-yellow-600">Required</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="w-4 h-4 text-yellow-600" />
                    <span className="font-medium text-yellow-800">Action Required</span>
                  </div>
                  <p className="text-sm text-yellow-700">
                    Zimbabwe transit bond needs to be purchased. Estimated cost: USD 150
                  </p>
                  <Button size="sm" className="mt-2 bg-yellow-600 hover:bg-yellow-700">
                    Purchase Bond Online
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Dangerous Goods</Label>
                    <div className="bg-gray-100 p-3 rounded text-sm">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-sars-green" />
                        <span>No DG cargo declared</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <Label>Compliance Score</Label>
                    <div className="bg-sars-green/10 p-3 rounded text-sm">
                      <div className="text-2xl font-bold text-sars-green">88%</div>
                      <p className="text-xs text-gray-600">Ready for border</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-terracotta">
                  <FileText className="w-5 h-5" />
                  RFM Generation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-sars-green/10 border border-sars-green/20 rounded p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <CheckCircle className="w-5 h-5 text-sars-green" />
                    <span className="font-medium text-sars-green">RFM Ready for Generation</span>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>RFM Number:</span>
                      <span className="font-medium">RFM-2024-016</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Route:</span>
                      <span className="font-medium">CPT → JHB → HRE</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Vehicle:</span>
                      <span className="font-medium">{formData.vehicleReg || 'GP 123-456'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Driver:</span>
                      <span className="font-medium">{formData.driverName || 'Driver Name'}</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Button variant="outline" className="h-16 flex flex-col">
                    <FileText className="w-5 h-5 mb-1" />
                    <span className="text-xs">SARS DA185</span>
                  </Button>
                  <Button variant="outline" className="h-16 flex flex-col">
                    <FileText className="w-5 h-5 mb-1" />
                    <span className="text-xs">CMR Note</span>
                  </Button>
                </div>

                <div className="bg-terracotta/10 border border-terracotta/20 rounded p-3">
                  <h4 className="font-medium text-terracotta mb-2">📱 Driver Mobile Access</h4>
                  <p className="text-sm text-gray-600">
                    QR code will be generated for driver mobile access to documents and route updates.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-terracotta text-white p-6">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/rfm')}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div className="flex-1">
            <h1 className="text-xl font-bold">Create Road Freight Manifest</h1>
            <p className="text-orange-200">Step {currentStep} of {steps.length}: {steps[currentStep - 1]?.title}</p>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <Progress value={progress} className="h-2" />
          <div className="flex justify-between text-xs text-orange-200">
            {steps.map((step) => {
              const StepIcon = step.icon;
              return (
                <div key={step.id} className="flex flex-col items-center">
                  <StepIcon className={`w-4 h-4 ${currentStep >= step.id ? 'text-white' : 'text-orange-300'}`} />
                  <span className={currentStep >= step.id ? 'text-white' : 'text-orange-300'}>
                    {step.title.split(' ')[0]}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {renderStepContent()}
      </div>

      {/* Navigation Buttons */}
      <div className="fixed bottom-20 left-0 right-0 bg-white border-t p-4">
        <div className="flex justify-between gap-4">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="flex-1"
          >
            Previous
          </Button>
          <Button
            onClick={handleNext}
            className="flex-1 bg-terracotta hover:bg-orange-700"
          >
            {currentStep === steps.length ? 'Generate RFM' : 'Next'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default RFMCreationScreen;
