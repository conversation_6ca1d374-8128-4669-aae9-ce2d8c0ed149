
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { 
  Plus, 
  Truck, 
  MapPin, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Route,
  FileText,
  Bell,
  TrendingUp,
  Home,
  User
} from "lucide-react";
import BottomNavigation from "@/components/BottomNavigation";

const RFMDashboard = () => {
  const navigate = useNavigate();

  const recentRFMs = [
    { id: "RFM-2024-015", route: "CPT → JHB → HRE", status: "In Transit", vehicle: "CA 123-456", driver: "<PERSON><PERSON>", eta: "2024-01-16 14:30" },
    { id: "RFM-2024-014", route: "DBN → MSU → GBE", status: "Border Delays", vehicle: "KZN 789-012", driver: "S. Ncube", eta: "2024-01-15 16:45" },
    { id: "RFM-2024-013", route: "JHB → PLZ → LUN", status: "Delivered", vehicle: "GP 345-678", driver: "T. Banda", eta: "Completed" },
    { id: "RFM-2024-012", route: "CPT → UPT → WDH", status: "Draft", vehicle: "WC 901-234", driver: "P. Williams", eta: "Pending" },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Delivered": return <CheckCircle className="w-4 h-4 text-sars-green" />;
      case "In Transit": return <Truck className="w-4 h-4 text-terracotta" />;
      case "Border Delays": return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      default: return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Delivered": return "text-sars-green";
      case "In Transit": return "text-terracotta";
      case "Border Delays": return "text-yellow-600";
      default: return "text-gray-600";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-gradient-to-r from-terracotta to-orange-600 text-white p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Road Freight Manifest</h1>
            <p className="text-orange-200">Cross-border road transport management</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
              <Bell className="w-5 h-5" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-white hover:bg-white/10"
              onClick={() => navigate('/dashboard')}
            >
              <Home className="w-5 h-5" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-white hover:bg-white/10"
              onClick={() => navigate('/profile')}
            >
              <User className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Stats Card */}
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardContent className="p-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">24</div>
                <div className="text-xs text-orange-200">Active Routes</div>
              </div>
              <div>
                <div className="text-2xl font-bold">92%</div>
                <div className="text-xs text-orange-200">Compliance Rate</div>
              </div>
              <div>
                <div className="text-2xl font-bold flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-sars-green mr-1" />
                  12
                </div>
                <div className="text-xs text-orange-200">This Week</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="p-6">
        <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-3 gap-4 mb-8">
          <Button
            onClick={() => navigate('/rfm/create')}
            className="h-20 flex flex-col bg-terracotta hover:bg-orange-700"
          >
            <Plus className="w-6 h-6 mb-1" />
            <span className="text-xs">Create RFM</span>
          </Button>
          <Button
            variant="outline"
            className="h-20 flex flex-col border-terracotta text-terracotta hover:bg-terracotta hover:text-white"
          >
            <Route className="w-6 h-6 mb-1" />
            <span className="text-xs">Route Templates</span>
          </Button>
          <Button
            variant="outline"
            className="h-20 flex flex-col border-terracotta text-terracotta hover:bg-terracotta hover:text-white"
          >
            <MapPin className="w-6 h-6 mb-1" />
            <span className="text-xs">Border Planner</span>
          </Button>
        </div>

        {/* Status Overview */}
        <div className="grid grid-cols-2 gap-4 mb-8">
          <Card className="border-terracotta/20 bg-terracotta/5">
            <CardHeader className="pb-2">
              <CardTitle className="text-terracotta text-sm">In Transit</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-terracotta">8</div>
              <p className="text-xs text-gray-600">Active shipments</p>
            </CardContent>
          </Card>
          
          <Card className="border-yellow-500/20 bg-yellow-50">
            <CardHeader className="pb-2">
              <CardTitle className="text-yellow-600 text-sm">Border Delays</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">3</div>
              <p className="text-xs text-gray-600">Avg. 6.5hr delay</p>
            </CardContent>
          </Card>
        </div>

        {/* Recent RFMs */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Active RFMs</h2>
          <div className="space-y-3">
            {recentRFMs.map((rfm) => (
              <Card key={rfm.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-terracotta">{rfm.id}</span>
                        <span className="text-xs px-2 py-1 rounded-full bg-orange-100 text-terracotta">
                          RFM
                        </span>
                        {getStatusIcon(rfm.status)}
                      </div>
                      <p className="text-sm text-gray-700 mb-1">
                        {rfm.route}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>🚛 {rfm.vehicle}</span>
                        <span>👤 {rfm.driver}</span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">ETA: {rfm.eta}</p>
                    </div>
                    <div className={`text-sm font-medium ${getStatusColor(rfm.status)}`}>
                      {rfm.status}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Road Freight Insights */}
        <Card className="border-terracotta/20 bg-terracotta/5">
          <CardHeader className="pb-3">
            <CardTitle className="text-terracotta text-sm">Road Freight Stats</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-700">Avg. border time:</span>
                <span className="font-medium">6.5hr</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-700">Compliance rate:</span>
                <span className="font-medium text-sars-green">92%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-700">Fuel efficiency:</span>
                <span className="font-medium">4.2L/100km</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Floating Action Button */}
      <Button
        onClick={() => navigate('/rfm/create')}
        className="fixed bottom-24 right-6 w-14 h-14 rounded-full bg-terracotta hover:bg-orange-700 shadow-lg animate-pulse-glow"
        size="sm"
      >
        <Plus className="w-6 h-6" />
      </Button>

      <BottomNavigation currentPage="rfm" />
    </div>
  );
};

export default RFMDashboard;
