
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { FileText, Plus, Upload, BookOpen, TrendingUp, Clock, CheckCircle, Scan } from "lucide-react";
import BottomNavigation from "@/components/BottomNavigation";

const BOEDashboard = () => {
  const navigate = useNavigate();

  const recentBOEs = [
    { id: "BOE-2024-001", supplier: "Tech Suppliers Ltd", status: "Approved", date: "2024-01-15", value: "R 45,200" },
    { id: "BOE-2024-002", supplier: "Import Express SA", status: "Processing", date: "2024-01-14", value: "R 87,150" },
    { id: "BOE-2024-003", supplier: "Global Trade Co", status: "Draft", date: "2024-01-13", value: "R 23,800" },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Approved": return <CheckCircle className="w-4 h-4 text-sars-green" />;
      case "Processing": return <Clock className="w-4 h-4 text-yellow-500" />;
      default: return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Approved": return "text-sars-green";
      case "Processing": return "text-yellow-600";
      default: return "text-gray-600";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-sars-blue text-white p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">BOE Center</h1>
            <p className="text-blue-200">Manage Bills of Entry & customs declarations</p>
          </div>
          <FileText className="w-8 h-8 text-white/80" />
        </div>

        {/* Status Summary Cards */}
        <div className="grid grid-cols-3 gap-4">
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">5</div>
              <div className="text-xs text-blue-200">Draft BOEs</div>
            </CardContent>
          </Card>
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">3</div>
              <div className="text-xs text-blue-200">Processing</div>
            </CardContent>
          </Card>
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">12</div>
              <div className="text-xs text-blue-200">SARS Approved</div>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="p-6">
        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-3 gap-4">
            <Button
              onClick={() => navigate('/capture')}
              className="h-20 flex flex-col bg-sars-blue hover:bg-blue-700 text-white"
            >
              <Scan className="w-6 h-6 mb-1" />
              <span className="text-xs">Scan Invoice</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col border-sars-blue text-sars-blue hover:bg-sars-blue hover:text-white"
            >
              <Upload className="w-6 h-6 mb-1" />
              <span className="text-xs">Upload Documents</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col border-gray-300 text-gray-600 hover:bg-gray-100"
            >
              <BookOpen className="w-6 h-6 mb-1" />
              <span className="text-xs">BOE Templates</span>
            </Button>
          </div>
        </div>

        {/* Recent BOEs */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Recent BOEs</h2>
          <div className="space-y-3">
            {recentBOEs.map((boe) => (
              <Card key={boe.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sars-blue">{boe.id}</span>
                        {getStatusIcon(boe.status)}
                      </div>
                      <p className="text-sm text-gray-600">{boe.supplier}</p>
                      <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                        <span>{boe.date}</span>
                        <span className="font-medium text-sars-blue">{boe.value}</span>
                      </div>
                    </div>
                    <div className={`text-sm font-medium ${getStatusColor(boe.status)}`}>
                      {boe.status}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* BOE Statistics */}
        <Card className="border-sars-blue/20 bg-sars-blue/5">
          <CardHeader className="pb-3">
            <CardTitle className="text-sars-blue text-sm flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              BOE Statistics
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-sars-blue">R 487,250</div>
                <div className="text-xs text-gray-600">Total Value (Month)</div>
              </div>
              <div>
                <div className="text-lg font-bold text-sars-green">94%</div>
                <div className="text-xs text-gray-600">Approval Rate</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Floating Action Button */}
      <Button
        onClick={() => navigate('/capture')}
        className="fixed bottom-24 right-6 w-14 h-14 rounded-full bg-sars-blue hover:bg-blue-700 shadow-lg"
        size="sm"
      >
        <Plus className="w-6 h-6" />
      </Button>

      <BottomNavigation currentPage="boe" />
    </div>
  );
};

export default BOEDashboard;
