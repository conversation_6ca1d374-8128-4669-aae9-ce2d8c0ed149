
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { 
  Plane, 
  Plus, 
  Package, 
  FileText, 
  Clock, 
  CheckCircle, 
  TrendingUp,
  MapPin,
  Timer,
  Target
} from "lucide-react";
import BottomNavigation from "@/components/BottomNavigation";

const AWBDashboard = () => {
  const navigate = useNavigate();

  const recentAWBs = [
    { id: "AWB-180-12345678", airline: "SAA", flight: "SA201", route: "JNB-LHR", status: "In Transit", date: "2024-01-15" },
    { id: "AWB-125-87654321", airline: "LH", flight: "LH572", route: "CPT-FRA", status: "Delivered", date: "2024-01-14" },
    { id: "AWB-074-11223344", airline: "EK", flight: "EK761", route: "JNB-DXB", status: "Awaiting Flight", date: "2024-01-13" },
    { id: "AWB-180-99887766", airline: "QR", flight: "QR1364", route: "CPT-DOH", status: "Draft", date: "2024-01-12" },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Delivered": return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "In Transit": return <Plane className="w-4 h-4 text-sky-500" />;
      case "Awaiting Flight": return <Clock className="w-4 h-4 text-yellow-500" />;
      default: return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Delivered": return "text-green-600";
      case "In Transit": return "text-sky-600";
      case "Awaiting Flight": return "text-yellow-600";
      default: return "text-gray-600";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-gradient-to-r from-sky-500 to-sky-600 text-white p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">AWB Center</h1>
            <p className="text-sky-200">Air cargo document management</p>
          </div>
          <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
            <MapPin className="w-5 h-5" />
          </Button>
        </div>

        {/* Air Cargo Stats Card */}
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardContent className="p-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">24</div>
                <div className="text-xs text-sky-200">Active AWBs</div>
              </div>
              <div>
                <div className="text-2xl font-bold">18hr</div>
                <div className="text-xs text-sky-200">Avg. Transit</div>
              </div>
              <div>
                <div className="text-2xl font-bold flex items-center justify-center">
                  <Target className="w-6 h-6 text-green-300 mr-1" />
                  92%
                </div>
                <div className="text-xs text-sky-200">On-time Delivery</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="p-6">
        <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-3 gap-4 mb-8">
          <Button
            onClick={() => navigate('/awb/create')}
            className="h-20 flex flex-col bg-sky-500 hover:bg-sky-600"
          >
            <Plus className="w-6 h-6 mb-1" />
            <span className="text-xs">Create AWB</span>
          </Button>
          <Button
            onClick={() => navigate('/awb/import')}
            variant="outline"
            className="h-20 flex flex-col border-sky-500 text-sky-500 hover:bg-sky-500 hover:text-white"
          >
            <Package className="w-6 h-6 mb-1" />
            <span className="text-xs">Import Manifest</span>
          </Button>
          <Button
            onClick={() => navigate('/awb/tracker')}
            variant="outline"
            className="h-20 flex flex-col border-sky-500 text-sky-500 hover:bg-sky-500 hover:text-white"
          >
            <Plane className="w-6 h-6 mb-1" />
            <span className="text-xs">Flight Tracker</span>
          </Button>
        </div>

        {/* Status Tiles */}
        <div className="grid grid-cols-2 gap-4 mb-8">
          <Card className="border-sky-200 bg-sky-50">
            <CardContent className="p-4 text-center">
              <FileText className="w-8 h-8 mx-auto text-sky-600 mb-2" />
              <div className="text-2xl font-bold text-sky-600">8</div>
              <div className="text-sm text-sky-700">Draft AWBs</div>
            </CardContent>
          </Card>
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="p-4 text-center">
              <Clock className="w-8 h-8 mx-auto text-yellow-600 mb-2" />
              <div className="text-2xl font-bold text-yellow-600">5</div>
              <div className="text-sm text-yellow-700">Awaiting Flight</div>
            </CardContent>
          </Card>
          <Card className="border-sky-200 bg-sky-50">
            <CardContent className="p-4 text-center">
              <Plane className="w-8 h-8 mx-auto text-sky-600 mb-2" />
              <div className="text-2xl font-bold text-sky-600">7</div>
              <div className="text-sm text-sky-700">In Transit</div>
            </CardContent>
          </Card>
          <Card className="border-green-200 bg-green-50">
            <CardContent className="p-4 text-center">
              <CheckCircle className="w-8 h-8 mx-auto text-green-600 mb-2" />
              <div className="text-2xl font-bold text-green-600">4</div>
              <div className="text-sm text-green-700">Delivered</div>
            </CardContent>
          </Card>
        </div>

        {/* Recent AWBs */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Recent AWBs</h2>
          <div className="space-y-3">
            {recentAWBs.map((awb) => (
              <Card key={awb.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sky-600">{awb.id}</span>
                        <span className="text-xs px-2 py-1 rounded-full bg-sky-100 text-sky-600">
                          {awb.airline}
                        </span>
                        {getStatusIcon(awb.status)}
                      </div>
                      <p className="text-sm text-gray-600">
                        {awb.flight} • {awb.route}
                      </p>
                      <p className="text-xs text-gray-500">{awb.date}</p>
                    </div>
                    <div className={`text-sm font-medium ${getStatusColor(awb.status)}`}>
                      {awb.status}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Air Cargo Updates */}
        <Card className="border-sky-200 bg-sky-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-sky-600 text-sm">Air Cargo Updates</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-sm text-gray-700">
              New IATA cargo rates effective January 2024. Updated dangerous goods regulations now in effect.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Floating Action Button */}
      <Button
        onClick={() => navigate('/awb/create')}
        className="fixed bottom-24 right-6 w-14 h-14 rounded-full bg-sky-500 hover:bg-sky-600 shadow-lg"
        size="sm"
      >
        <Plus className="w-6 h-6" />
      </Button>

      <BottomNavigation currentPage="awb" />
    </div>
  );
};

export default AWBDashboard;
